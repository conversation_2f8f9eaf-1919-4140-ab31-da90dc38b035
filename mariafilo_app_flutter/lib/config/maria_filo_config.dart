import 'package:soma_core/config/models/checking_account_config.dart';
import 'package:soma_core/config/models/credit_voucher_config.dart';
import 'package:soma_core/soma_core.dart';

Config coreConfigBuilder() {
  final mariafiloConfig = Config(
    somaBusConfig: productionPosSoma,
    prismicConfig: productionPrismicConfig,
    cmsConfig: productionCmsConfig,
    store: Store(
      defaultSalesChannel: 7,
      storeName: "mariafilo",
      storeUrl: "https://www.mariafilo.com.br",
      utmiCampaign: UTMICampaign(
        addCode: "VendMF",
        removeCode: "",
      ),
    ),
    masterData: MasterData(
      salesPersonTable: 'VD',
      fieldCodeID: 'cod',
      fieldNameID: 'name',
    ),
    appUrls: const AppUrls(
      privacyWarning: 'https://www.mariafilo.com.br/aviso-de-privacidade',
      faq: 'https://centraldeatendimento.mariafilo.com.br/hc/pt-br',
      termsAndConditions: 'https://www.mariafilo.com.br/termos-e-condicoes',
      devolutionRouteUrlTemplate:
          'https://mariafilo.troque.app.br?order={orderId}&client={userEmail}',
      oneTrustDeleteAccountUrl:
          "https://azzas2154-privacy.my.onetrust.com/webform/************************************/f8d511c1-5d00-42d1-b4d9-89375a37000f",
    ),
    appFeaturesConfig: AppFeaturesConfig(
      showProductTitleEmptyDesc: false,
      showProducRefCodeBelowSimilarPDP: false,
      showProducRefCodeBelowTitlePDP: true,
      giftWrapConfig: const GiftWrapConfig(),
      giftCardProductConfig: GiftCardProductConfig(isEnabled: false),
      giftCardConfig: const GiftCardConfig(isEnabled: false),
      checkingAccountConfig: const CheckingAccountConfig(isEnabled: true),
      trackingUrl: true,
      creditVoucherConfig: CreditVoucherConfig(provider: '', isEnabled: false),
      storeUpdateApp: true,
      storePickupConfig: const StorePickupConfig(),
      fullLookConfig: const FullLookConfig(
        isEnabled: true,
        productsSource: FullLookProductsSource.accessories,
      ),
      showTotalAndMaxInstallmentsCheckoutResume: true,
      enablePixInstallments: true,
    ),
    buttonsRedirects: ButtonsRedirects(
      genericError: Search(
          clusterId: 910,
          filterCategoryOrCluster: '/departamento/loja/categoria/roupas',
          orderBy: OrderBy.orderByReleaseDateDESC),
      emptyBag: Search(
          clusterId: 910,
          filterCategoryOrCluster: '/departamento/loja/categoria/roupas',
          orderBy: OrderBy.orderByReleaseDateDESC),
    ),
    tabItems: TabItemConfig(tabItems: {
      'home': TabItem(index: 0, name: 'Inicio'),
      'wishlist': TabItem(index: 1, name: 'Wishlist'),
      'explore': TabItem(index: 2, name: 'Menu'),
      'profile': TabItem(index: 3, name: 'Perfil'),
      'bag': TabItem(index: 4, name: 'Sacola'),
    }),
    intelligentSearchConfig: IntelligentSearchConfig(
      sellername: '',
      filters: {
        ...IntelligentSearchConfig.defaultFiltersConfigFor([
          FilterType.categories,
          FilterType.sizes,
          FilterType.colors,
          FilterType.price,
        ]),
      },
    ),
    strapiConfig: const StrapiConfig(
      accessToken:
          '6ff4761b9585c8e1d0ef42f054fd334d9acaac7937ed849c719df9a308cc8e3ef2645deff51495f0231f4e5d3e793979a0d4ae1031f38fa91c5c1fec95b47df323df298c030ff42dd0caf916805a550dfc3552aca8a1f80afff93629f3000f2b1601717efb7a1c2f0e332bd0fd66e75850d8a896fa909368693cdac50ca26b64',
    ),
    orderFormConfig: const OrderFormConfig(
      maxSameItemQuantity: 6,
    ),
    whatsapp: Whatsapp(
      phone: '+552125036852',
    ),
    recaptchaConfig: const RecaptchaConfig(
      androidKey: '6LcqneQnAAAAAAj7OrnFZd4KOr0F809CnA5ET3fJ',
      iosKey: '6Lfoc-QnAAAAAPu5uTZotM50tuu_ehFY6JpMGBII',
    ),
    maxDiscountConfig: const MaxDiscountConfig(maxDiscount: 80),
  );

  return mariafiloConfig;
}

const productionPrismicConfig = PrismicConfig(
  baseUrl: 'https://somabus-kong.somalabs.com.br/api/cms-cache/mariafiloapp/',
  accessToken:
      'MC5ZeU0xRHhNQUFDTUFCcGNl.eu-_ve-_ve-_vT3vv73vv71Sb3Ef77-9We-_ve-_ve-_vU7vv71k77-9aWTvv70i77-9fO-_ve-_ve-_ve-_vUIX',
);

const stagingPrismicConfig = PrismicConfig(
  baseUrl:
      'https://somabus-kong.somalabs.com.br/api/cms-cache/mariafiloapphomolog/',
  accessToken:
      'MC5ZMkdBeHhFQUFDRUFJOHc3.IXHvv73vv70g77-9YO-_ve-_ve-_vVXvv73vv71KdGrvv70fUO-_ve-_vQM5PO-_vTnvv73vv70jHnfvv70',
);

const productionCmsConfig = CmsConfig(
  baseUrl: 'https://cms-mf.somalabs.com.br/api/',
  accessToken:
      '7fbd2cb102b0f45c0fa1ac2bc872fccbf8f3cd217dd377123c9c1a46ea3e95b7426dca7659c618565887ae40f2120ebc7bf03d13fce113f29e4cf155c70ebd3e0ccb3be421180a8aec76d3f125ea5616663cf139562f94b45f53599690a9882f9e62a46778fd694ba80ec5f469a24edefb4a28e241391acbfe0754191e185815',
);

const stagingCmsConfig = CmsConfig(
  baseUrl: 'https://cms-mf-homolog.somalabs.com.br/api/',
  accessToken:
      '1d75acec0ce30007062e007535df81f5312f2685af7b9bc552b0087a061637f1a7b291a934ef399066b3cb4c60889f32c1a291d141639c1c386bae3a2ae9d1fe68181e4418e9b12d2fcea39e40ae802b999eeb8253a0e3e415f36e8fce3aa8a651c70169324a366bcb14a548439ae33075c7b9fccfa9b4ac5ba0d06ea05edbc4',
);

const localCmsConfig = CmsConfig(
  baseUrl: 'http://localhost:1337/api/',
  accessToken: '',
);

final productionPosSoma = SomaBusConfig(
  posSoma: 'OGMyMzYzZDYtMjQxOC00ZjBhLWJlNWItNWJlZWViYjNhNTg3',
);

final homologPosSoma = SomaBusConfig(
  posSoma: 'Y2E4NGIxYTItNmU2NC00NTQ3LWIyYTMtY2QxYmQyYjQ4Y2Rh',
);
