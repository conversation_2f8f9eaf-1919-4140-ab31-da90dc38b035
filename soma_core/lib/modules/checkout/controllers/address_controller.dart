import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:soma_analytics/soma_analytics.dart';
import 'package:soma_core/modules/auth/models/user_orders/logistics_info.dart'
    as auth_log;
import 'package:soma_core/modules/auth/models/user_orders/shipping_data.dart';
import 'package:soma_core/modules/checkout/models/address_shipping_option_request/logistics_info.dart'
    as checkout_log;
import 'package:soma_core/modules/checkout/models/shipping_options/options.dart'
    as optionmodel;
import 'package:soma_core/modules/checkout/models/shipping_options/slas.dart';
import 'package:soma_core/modules/location/events/simulate_shipping_option_event.dart';

import '../../../soma_core.dart';
import '../models/address_shipping_option_request/address.dart';
import '../models/index.dart';
import '../models/query_cep.dart';
import '../models/shipping_options/pickup_options.dart';
import '../models/shipping_options/selected_delivery.dart';
import '../models/shipping_options/shipping_options.dart';
import '../models/shipping_options/sla.dart';
import '../services/address_service.dart';
import '../utils/update_order_form.dart';

enum ShippingType {
  delivery,
  pickup;

  bool get isDelivery => this == ShippingType.delivery;

  bool get isPickup => this == ShippingType.pickup;
}

class AddressController extends GetxController with UpdateOrderForm {
  final AddressService service;
  final Rxn<OrderForm> orderForm;
  final SomaAnalyticsController somaAnalyticsController;
  final Events events;

  @override
  final KeyValueStorage storage;

  Rxn<Address> addressInfo = Rxn<Address>(null);

  final isCepInvalid = false.obs;
  final invalidCepMessage = ''.obs;
  bool backToOrderReview = false;

  Rxn<bool> isLoading = Rxn<bool>(false);

  Rxn<ShippingOptionsNew> shippingOptionsNewData =
      Rxn<ShippingOptionsNew>(null);

  Rxn<ShippingOptions> shippingOptionsData = Rxn<ShippingOptions>(null);

  ///Define qual opcao foi escolhida pelo user ou vindo do OrderForm
  Rx<ShippingType> shippingType = Rx<ShippingType>(ShippingType.delivery);

  //Tudo relacionado ao tipo entrega
  Rxn<DeliveryOption> deliveryOptionSelected = Rxn<DeliveryOption>(null);
  Rxn<List<PackageModel>> controllerPackages = Rxn<List<PackageModel>>(null);

  //Tudo relacionado ao tipo retirada
  Rxn<Slas> pickupPointOptionSelected = Rxn<Slas>(null);

  AddressController(
    this.service,
    this.orderForm,
    this.somaAnalyticsController,
    this.storage,
    this.events,
  ) {
    orderForm.listen(_onOrderFormUpdate);
  }

  void _onOrderFormUpdate(OrderForm? orderForm) {
    final shippingOptions = orderForm?.shippingData?.shippingOptions;
    if (shippingOptions != null) {
      shippingOptionsData.value = shippingOptions;
    }

    final shippingOptionsNew = orderForm?.shippingData?.shippingOptionsNew;
    if (shippingOptionsNew != null) {
      shippingOptionsNewData.value = shippingOptionsNew;
    }
  }

  Future<OrderForm> selectPickupOption({
    required PickupOptions pickupInPoint,
    required PickupStore store,
    required String receiverName,
  }) async {
    final storePickupConfig =
        service.config.appFeaturesConfig.storePickupConfig;
    if (storePickupConfig?.isEnabled ?? false) {
      final pickupSlas =
          _getSlasForPickup(pickupInPoint: pickupInPoint, store: store);
      final slasToAdd =
          joinDeliveryInfo(selectedDelivery?.slas ?? <Sla>[], pickupSlas);
      addressInfo.value =
          addressInfo.value?.copyWith(receiverName: receiverName) ??
              Address(receiverName: receiverName);
      final delivery = SelectedDelivery.fromPickupInPoint(
        pickupInPoint: pickupInPoint,
        selectedStore: store,
        slas: slasToAdd,
      );
      return selectDelivery(delivery);
    } else {
      throw const UseOfDisabledFeatureException.storePickup();
    }
  }

  List<Sla> _getSlasForPickup({
    required PickupOptions pickupInPoint,
    required PickupStore store,
  }) {
    final shippingOptions = shippingOptionsData.value;
    final slasInSameStore =
        pickupInPoint.slas.where((s) => s.slas?.id == store.id);
    final slas = [...slasInSameStore];
    if (shippingOptions != null && !shippingOptions.hasMultipleOptions) {
      final selectedProductsIds = slasInSameStore.map((s) => s.itemId).toSet();
      slas.addAll(_getRemainingProductsDeliverySlas(
        shippingOptions: shippingOptions,
        selectedProductsIds: selectedProductsIds,
      ));
    }
    return slas;
  }

  SelectedDelivery? get selectedDelivery {
    final logisticsInfo = orderForm.value?.shippingData?.logisticsInfo;
    if (logisticsInfo != null) {
      return SelectedDelivery.fromLogisticsInfo(logisticsInfo);
    } else {
      return null;
    }
  }

  void selectAddressFromOrderForm({OrderForm? orderForm}) {
    orderForm ??= this.orderForm.value;
    final userAddress = orderForm?.shippingData?.selectedAddresses?.firstOrNull;

    if (userAddress != null) {
      final newAddress = completeAddress(Address(
        receiverName: userAddress.receiverName,
        postalCode: userAddress.postalCode,
        city: userAddress.city,
        state: userAddress.state,
        street: userAddress.street,
        number: userAddress.number,
        neighborhood: userAddress.neighborhood,
        complement: userAddress.complement,
        country: userAddress.country,
        geoCoordinates: userAddress.geoCoordinates
                ?.toList()
                .map((c) => c.toDouble())
                .toList() ??
            [],
      ));
      if (newAddress != addressInfo.value) {
        addressInfo.value = newAddress;
      }
    }
  }

  Address completeAddress(Address selectedAddress) {
    final mostCompleteAddress = orderForm
        .value!.shippingData!.availableAddresses!
        .where((address) =>
            (selectedAddress.postalCode == address.postalCode &&
                address.number != null &&
                address.number!.isNotEmpty &&
                address.complement != null &&
                address.complement!.isNotEmpty) ||
            (selectedAddress.postalCode == address.postalCode &&
                address.number != null &&
                address.number!.isNotEmpty))
        .firstOrNull;
    return Address(
      receiverName: selectedAddress.receiverName,
      postalCode: selectedAddress.postalCode,
      city: selectedAddress.city,
      state: selectedAddress.state,
      street: selectedAddress.street,
      number: selectedAddress.number ?? mostCompleteAddress?.number,
      neighborhood: selectedAddress.neighborhood,
      complement: selectedAddress.complement ?? mostCompleteAddress?.complement,
      country: selectedAddress.country,
      geoCoordinates: selectedAddress.geoCoordinates?.toList() ?? [],
    );
  }

  Future<void> updateShipping() async {
    if (orderForm.value?.shippingData?.address?.postalCode != null) {
      var address = orderForm.value!.shippingData!.address;

      addressInfo.value = completeAddress(Address(
        receiverName: address?.receiverName,
        postalCode: address?.postalCode,
        city: address?.city,
        state: address?.state,
        street: address?.street,
        number: address?.number,
        neighborhood: address?.neighborhood,
        complement: address?.complement,
        country: address?.country,
        geoCoordinates: address?.geoCoordinates?.toList() ?? [],
      ));

      shippingOptions(cep: address!.postalCode!);
    }
  }

  Future<Address> getAddressFromCep({required String cep}) async {
    isLoading(true);
    try {
      final response = await service.queryCep(cep: cep);
      final address = Address(
        city: response.city,
        complement: response.complement,
        country: response.country,
        geoCoordinates: response.geoCoordinates,
        neighborhood: response.neighborhood,
        number: response.number,
        postalCode: response.postalCode,
        reference: response.reference,
        state: response.state,
        street: response.street,
      );
      return address;
    } on DioException catch (e) {
      if (e.response?.data['message'].contains('inválido ou não encontrado')) {
        throw InvalidCepException(e.response?.data['message'], cause: e);
      }
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<void> queryCep({required String cep}) async {
    try {
      isLoading(true);
      isCepInvalid(false);
      final response = await service.queryCep(cep: cep);
      addressInfo(Address(
          city: response.city,
          complement: response.complement,
          country: response.country,
          geoCoordinates: response.geoCoordinates,
          neighborhood: response.neighborhood,
          number: response.number,
          postalCode: response.postalCode,
          reference: response.reference,
          state: response.state,
          street: response.street,
          receiverName: addressInfo.value?.receiverName ?? ""));

      await shippingOptions(cep: cep);
    } on DioException catch (e) {
      if (e.response?.data['message'].contains('inválido ou não encontrado')) {
        isCepInvalid(true);
        invalidCepMessage(e.response?.data['message']);
        throw SomaCoreExceptionInvalidPostCode(
          message: e.response?.data['message'],
          cause: e,
        );
      }

      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<ShippingOptionsNew> shippingOptions({
    required String cep,
  }) async {
    try {
      isLoading(true);
      final newOrderForm = await service.shippingOptions(
        orderFormId: orderForm.value!.orderFormId!,
        cep: cep,
      );
      orderForm.value = newOrderForm;
      updateOrder(newOrderForm);
      final ShippingOptionsNew shippingOptions =
          newOrderForm.shippingData?.shippingOptionsNew ??
              const ShippingOptionsNew();

      updateShippingOptions();

      return shippingOptions;
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  String getDeliveryPrice(List<PackageModel>? packages) {
    if (packages == null) return '';
    final deliveryPrice = packages.fold(0,
        (soma, element) => soma + (element.deliveryOptionSelected.price ?? 0));
    return deliveryPrice == 0
        ? 'Grátis'
        : CurrencyUtils.format(
            amount: deliveryPrice,
            dividedBy100: true,
          );
  }

  String getDeliveryTime(List<PackageModel>? packages) {
    if (packages == null || packages.isEmpty) return '';

    final validHours = packages
        .map((element) {
          final estimate = element.deliveryOptionSelected.shippingEstimate;
          return estimate == null ? null : int.tryParse(estimate);
        })
        .where((hours) => hours != null && hours != -1)
        .cast<int>()
        .toList();

    if (validHours.isEmpty) return '';

    return formatDeliveryTimes(validHours);
  }

  String formatDeliveryTime(int hour) {
    String formatTime(int hours) {
      if (hours == 24) return '1 dia útil';
      if (hours > 24) return '${(hours / 24).ceil()} dias úteis';
      return '$hours horas';
    }

    String description = 'em até ${formatTime(hour)}';

    String formattedString = description;

    formattedString = formattedString.replaceFirst("em", "Em");

    return '$formattedString.';
  }

  String formatDeliveryTimes(List<int> validHours) {
    if (validHours.isEmpty) return '';

    validHours.sort();

    String formatTime(int hours) {
      if (hours == 24) return '1 dia útil';
      if (hours > 24) return '${(hours / 24).ceil()} dias úteis';
      return '$hours horas';
    }

    List<String> descriptions = validHours.map((hours) {
      return 'em até ${formatTime(hours)}';
    }).toList();

    String formattedString = descriptions.length > 1
        ? '${descriptions.sublist(0, descriptions.length - 1).join(', ')} e ${descriptions.last}'
        : descriptions.first;

    formattedString = formattedString.replaceFirst("em", "Em");

    return '${validHours.length} pacotes. $formattedString.';
  }

  void updateShippingOptions() {
    if (addressInfo.value == null) return;

    DeliveryOption? selectedDeliveryOption;
    deliveryOptionSelected.value = selectedDeliveryOption;

    final packages = handleDeliveryOptions();
    checkIsPickup();

    if (packages.isEmpty) return;
    if (packages.length > 1) {
      selectedDeliveryOption = CommonDeliveryOption(
        deliveryPrice: getDeliveryPrice(controllerPackages.value),
        deliveryTitle: 'Prazos variados',
        deliveryTime: getDeliveryTime(controllerPackages.value),
        id: '0',
        isSelected: true,
      );

      controllerPackages.value = packages;
      deliveryOptionSelected.value = selectedDeliveryOption;
    } else {
      selectedDeliveryOption = packages.isEmpty
          ? null
          : CommonDeliveryOption(
              id: packages.first.deliveryOptions
                  .indexWhere((element) =>
                      element.name ==
                      packages.first.deliveryOptionSelected.name)
                  .toString(),
              deliveryPrice: packages.first.price == 0
                  ? 'Grátis'
                  : CurrencyUtils.format(
                      amount: packages.first.price,
                      dividedBy100: true,
                    ),
              deliveryTitle: packages.first.deliveryOptionSelected.name ?? '',
              deliveryTime:
                  packages.first.deliveryOptionSelected.shippingEstimate ?? '',
            );

      controllerPackages.value = packages;
      deliveryOptionSelected.value = selectedDeliveryOption;
    }

    controllerPackages.value = packages;
    deliveryOptionSelected.value = selectedDeliveryOption;
  }

  void checkIsPickup() {
    final pickupData =
        orderForm.value?.shippingData?.shippingOptionsNew?.pickupInPoint;

    if (pickupData?.packs != null && (pickupData!.packs ?? []).isNotEmpty) {
      shippingType.value = ShippingType.pickup;
    } else {
      shippingType.value = ShippingType.delivery;
    }
  }

  List<PackageModel> handleDeliveryOptions() {
    final packages = <PackageModel>[];
    final deliveryData =
        orderForm.value?.shippingData?.shippingOptionsNew?.deliveryData;

    final deliveryOnePackage = deliveryData?.packs.length == 1;

    if (deliveryData?.packs != null && deliveryData!.packs.isNotEmpty) {
      for (var item in deliveryData.packs) {
        List<Slas> deliveryOptions = [];

        // add a opção de entrega selecionada como primeira opção de entrega
        final deliveryOptionSelected = Slas(
          id: item.selectedOption.slaId,
          name: item.selectedOption.slaId,
          price: item.selectedOption.total,
          deliveryChannel: item.selectedOption.deliveryChannel,
          shippingEstimate: item.selectedOption.shippingEstimate.toString(),
        );
        deliveryOptions.add(deliveryOptionSelected);

        // se só tem um pacote,
        // e tem outras opções pra esse pacote, add elas como opções de entrega
        if (deliveryOnePackage && item.otherOptions.isNotEmpty) {
          for (var option in item.otherOptions) {
            final dOption = Slas(
              id: option.id,
              name: option.selectedSla,
              price: option.total,
              deliveryChannel: option.deliveryChannel,
              shippingEstimate: option.shippingEstimate.toString(),
            );
            deliveryOptions.add(dOption);
          }
        }

        if (deliveryData.total == null) continue;
        final pack = PackageModel(
          price: deliveryData.total!,
          allItemsSameDelivery: deliveryOnePackage,
          items: item.itemsInPack.map((e) => e.id).toList(),
          deliveryOptionSelected: deliveryOptionSelected,
          deliveryOptions: deliveryOptions,
          imagesUrl: item.itemsInPack
              .map((e) => e.imageUrl)
              .whereType<String>()
              .toList(),
        );
        packages.add(pack);
      }
    }
    return packages;
  }

  Iterable<Sla> _getRemainingProductsDeliverySlas(
      {required ShippingOptions shippingOptions,
      required Set<String?> selectedProductsIds}) {
    //todo: helo

    final options = shippingOptions.economic ?? shippingOptions.fast;
    final remainingProductsSlas =
        options?.slas.where((s) => !selectedProductsIds.contains(s.itemId)) ??
            [];
    return remainingProductsSlas;
  }

  Future<void> selectDeliveryFor({
    required List<Sla> newSlas,
    String? receiverName,
  }) async {
    final slas = joinDeliveryInfo<ItemDeliveryInfo>(
      selectedDelivery?.slas ?? [],
      newSlas,
    );
    addressInfo.value = addressInfo.value?.copyWith(receiverName: receiverName);
    await _updateLogisticsInfo(
        address: addressInfo.value, selectedDelivery: slas);
  }

  List<T> joinDeliveryInfo<T extends ItemDeliveryInfo>(
    List<T> slas,
    List<T> newSlas,
  ) {
    final otherProductsSlas = slas
        .where((sla) => !newSlas.any((newSlas) => newSlas.itemId == sla.itemId))
        .toList();
    return [
      ...otherProductsSlas,
      ...newSlas,
    ];
  }

  Future<OrderForm> updateAddress() async {
    isLoading(true);

    List<checkout_log.LogisticsInfo>? logistics = [];

    for (auth_log.LogisticsInfo item
        in (orderForm.value?.shippingData?.logisticsInfo ?? [])) {
      logistics.add(checkout_log.LogisticsInfo(
        itemIndex: item.itemIndex,
        selectedSla: item.selectedSlaId,
        selectedDeliveryChannel: item.selectedDeliveryChannel,
      ));
    }

    try {
      final response = await service.selectShippingOption(
        orderFormId: orderForm.value!.orderFormId.toString(),
        address: addressInfo.value ?? Address(),
        logisticsInfo: logistics,
      );

      orderForm(response);
      updateOrder(response);

      return response;
    } finally {
      isLoading(false);
    }
  }

  Future<OrderForm> _updateLogisticsInfo({
    required Address? address,
    required List<ItemDeliveryInfo> selectedDelivery,
  }) async {
    selectedDelivery.sort((l1, l2) => l1.itemIndex!.compareTo(l2.itemIndex!));
    isLoading(true);
    try {
      final response = await service.selectShippingOption(
        orderFormId: orderForm.value!.orderFormId.toString(),
        address: address != null ? completeAddress(address) : null,
        logisticsInfo: selectedDelivery.toLogisticsInfo(),
      );
      somaAnalyticsController.logAddShippingInfo(
        orderForm: response,
        shippingTier: selectedDelivery.shippingTier,
        selectedDelivery: selectedDelivery,
      );

      orderForm(response);
      updateOrder(response);

      return response;
    } finally {
      isLoading(false);
    }
  }

  Future<OrderForm> selectShippingOption({
    required optionmodel.Options options,
  }) async {
    return selectDelivery(SelectedDelivery.fromOptions(options));
  }

  Future<void> updateDeliveryOption(DeliveryOption option,
      {bool keepPickup = false}) async {
    final package = controllerPackages.value?.first;
    final index = int.parse(option.id);

    if (package == null) return;

    if (package.deliveryOptions.isNotEmpty) {
      package.deliveryOptionSelected = package.deliveryOptions[index];
    }
    deliveryOptionSelected.value = option;

    if (keepPickup) {
      await updateOnlyDelivery();
    } else {
      await updateAddressAndDelivery();
    }
  }

  Future<void> updateOnlyDelivery({
    Address? address,
  }) async {
    List<checkout_log.LogisticsInfo>? logistics = [];

    for (auth_log.LogisticsInfo item
        in (orderForm.value?.shippingData?.logisticsInfo ?? [])) {
      final package = controllerPackages.value?.firstWhereOrNull(
        (e) {
          return e.items.contains(item.itemId);
        },
      );

      if (item.selectedDeliveryChannel == "pickup-in-point") {
        logistics.add(
          checkout_log.LogisticsInfo(
              selectedSla: item.selectedSlaId,
              selectedDeliveryChannel: item.selectedDeliveryChannel,
              itemIndex: item.itemIndex),
        );
      } else {
        logistics.add(
          checkout_log.LogisticsInfo(
              selectedSla: package?.deliveryOptionSelected.id,
              selectedDeliveryChannel:
                  package?.deliveryOptionSelected.deliveryChannel,
              itemIndex: item.itemIndex),
        );
      }
    }

    try {
      Address? currentAddress = address;
      currentAddress ??= addressInfo.value;
      addressInfo.value = currentAddress;

      final response = await service.selectShippingOption(
        orderFormId: orderForm.value!.orderFormId.toString(),
        address:
            currentAddress != null ? completeAddress(currentAddress) : null,
        logisticsInfo: logistics,
      );

      orderForm(response);
      updateOrder(response);
      updateShippingOptions();
    } catch (e) {
      print(e);
    }
  }

  Future<void> resetToDeliveryShipping({Address? address}) async {
    Address? currentAddress = address ?? addressInfo.value;
    addressInfo.value = currentAddress;

    final currentLogistics = orderForm.value?.shippingData?.logisticsInfo;
    if (currentLogistics == null) return;
    final List<checkout_log.LogisticsInfo> newLogistics = [];

    for (final item in currentLogistics) {
      newLogistics.add(
        checkout_log.LogisticsInfo(
          selectedSla: "Normal",
          selectedDeliveryChannel: "delivery",
          itemIndex: item.itemIndex,
        ),
      );
    }

    final response = await service.selectShippingOption(
      orderFormId: orderForm.value!.orderFormId.toString(),
      address: currentAddress != null ? completeAddress(currentAddress) : null,
      logisticsInfo: newLogistics,
    );

    orderForm(response);
    updateOrder(response);
    updateShippingOptions();
  }

  Future<void> setPickupShipping({
    required String slaId,
    required String slaDeliveryChannel,
    required List<String> itemIds,
    Address? address,
    String? receiverName,
  }) async {
    Address? currentAddress =
        address ?? addressInfo.value?.copyWith(receiverName: receiverName);
    addressInfo.value = currentAddress;

    final currentLogistics = orderForm.value?.shippingData?.logisticsInfo;
    if (currentLogistics == null) return;

    final List<checkout_log.LogisticsInfo> newLogistics = [];

    for (final item in currentLogistics) {
      if (itemIds.contains(item.itemId)) {
        newLogistics.add(
          checkout_log.LogisticsInfo(
            selectedSla: slaId,
            selectedDeliveryChannel: slaDeliveryChannel,
            itemIndex: item.itemIndex,
          ),
        );
      } else {
        newLogistics.add(
          checkout_log.LogisticsInfo(
            selectedSla: "Normal",
            selectedDeliveryChannel: "delivery",
            itemIndex: item.itemIndex,
          ),
        );
      }
    }

    final response = await service.selectShippingOption(
      orderFormId: orderForm.value!.orderFormId.toString(),
      address: currentAddress != null ? completeAddress(currentAddress) : null,
      logisticsInfo: newLogistics,
    );

    orderForm(response);
    updateOrder(response);
    updateShippingOptions();
  }

  Future<void> updateAddressAndDelivery({
    Address? address,
    List<checkout_log.LogisticsInfo>? logisticsInfo,
  }) async {
    final List<checkout_log.LogisticsInfo>? logistics = logisticsInfo ??
        orderForm.value!.shippingData?.logisticsInfo!.map((element) {
          final package = controllerPackages.value?.firstWhereOrNull(
            (e) => e.items.contains(element.itemId),
          );

          return checkout_log.LogisticsInfo(
              selectedSla: package?.deliveryOptionSelected.id,
              selectedDeliveryChannel:
                  package?.deliveryOptionSelected.deliveryChannel,
              itemIndex: element.itemIndex);
        }).toList();

    try {
      Address? currentAddress = address;
      currentAddress ??= addressInfo.value;
      addressInfo.value = currentAddress;

      final response = await service.selectShippingOption(
        orderFormId: orderForm.value!.orderFormId.toString(),
        address:
            currentAddress != null ? completeAddress(currentAddress) : null,
        logisticsInfo: logistics,
      );

      orderForm(response);
      updateOrder(response);
      updateShippingOptions();
    } catch (e) {
      print(e);
    }
  }

  Future<OrderForm> selectDelivery(SelectedDelivery delivery) async {
    try {
      isLoading(true);
      return await _updateLogisticsInfo(
        address: addressInfo.value,
        selectedDelivery: delivery.slas,
      );
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<ShippingOptionsNew> oldSimulateShippingOptions({
    required String postalCode,
    required List<SimulationProducts> products,
  }) async {
    postalCode = postalCode.replaceAll(r'\D', '');
    isLoading(true);
    try {
      final response = await service.simulateShippingOptions(
        postalCode: postalCode,
        products: products,
      );
      events.add(SimulateShippingOptionsEvent(postalCode));
      return response;
    } catch (e, st) {
      if (e is ShippingOptionsSimulationError) {
        rethrow;
      }
      debugPrintStack(stackTrace: st);
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<ShippingOptionsNew> simulateShippingOptions({
    required String postalCode,
    required List<SimulationProducts> products,
  }) async {
    postalCode = postalCode.replaceAll(r'\D', '');

    try {
      final response = await service.simulateShippingOptions(
        postalCode: postalCode,
        products: products,
      );

      events.add(SimulateShippingOptionsEvent(postalCode));

      return response;
    } catch (e, st) {
      if (e is ShippingOptionsSimulationError) {
        rethrow;
      }
      debugPrintStack(stackTrace: st);
      throw SomaCoreException(message: e.toString(), cause: e);
    }
  }

  void clearUserData() {
    orderForm.value = null;
    addressInfo.value = null;
    shippingOptionsNewData.value = null;
  }

  bool isAdressComplete() {
    final address = orderForm.value?.shippingData?.address;
    final localAddress = addressInfo.value;

    if (address == null || localAddress == null) {
      return false;
    }

    return address.isComplete && localAddress.isComplete;
  }

  Future<QueryCep> getAddressByGeolocalization({
    required String latitude,
    required String longitude,
  }) async {
    //TODO: retornar assim que for feito ajuste no barramento

    var address = await service.addressByGeolocalization(
      latitude: latitude,
      longitude: longitude,
    );

    address.postalCode = address.postalCode?.replaceAll('-', '');
    address.state = convertStateToUF(address.state ?? '');

    return address;
  }

  //TODO: remover método assim que for feito ajuste no barramento
  String convertStateToUF(String stateName) {
    if (stateName.isEmpty) {
      return '';
    }
    switch (stateName) {
      case "Rondônia":
        return "RO";
      case "Acre":
        return "AC";
      case "Amazonas":
        return "AM";
      case "Roraima":
        return "RR";
      case "Pará":
        return "PA";
      case "Amapá":
        return "AP";
      case "Tocantins":
        return "TO";
      case "Maranhão":
        return "MA";
      case "Piauí":
        return "PI";
      case "Ceará":
        return "CE";
      case "Rio Grande do Norte":
        return "RN";
      case "Paraíba":
        return "PB";
      case "Pernambuco":
        return "PE";
      case "Alagoas":
        return "AL";
      case "Sergipe":
        return "SE";
      case "Bahia":
        return "BA";
      case "Minas Gerais":
        return "MG";
      case "Espírito Santo":
        return "ES";
      case "Rio de Janeiro":
        return "RJ";
      case "São Paulo":
        return "SP";
      case "Paraná":
        return "PR";
      case "Santa Catarina":
        return "SC";
      case "Rio Grande do Sul":
        return "RS";
      case "Mato Grosso do Sul":
        return "MS";
      case "Mato Grosso":
        return "MT";
      case "Goiás":
        return "GO";
      case "Distrito Federal":
        return "DF";
      default:
        return "";
    }
  }

  Future<void> updateAddressByGeolocalization(
      {required String latitude, required String longitude}) async {
    try {
      isLoading(true);
      isCepInvalid(false);
      final response = await service.addressByGeolocalization(
          latitude: latitude, longitude: longitude);
      addressInfo(Address(
          city: response.city,
          complement: response.complement,
          country: response.country,
          geoCoordinates: response.geoCoordinates,
          neighborhood: response.neighborhood,
          number: response.number,
          postalCode: response.postalCode,
          reference: response.reference,
          state: response.state,
          street: response.street,
          receiverName: addressInfo.value?.receiverName ?? ""));
      if (addressInfo.value != null &&
          addressInfo.value!.postalCode!.isNotEmpty) {
        await shippingOptions(cep: addressInfo.value!.postalCode!);
      }
    } on DioException catch (e) {
      if (e.response?.data['message'].contains('inválido ou não encontrado')) {
        isCepInvalid(true);
        invalidCepMessage(e.response?.data['message']);
      }
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  void updateAdressWithFirstOrder(List<ListUserOrder>? orders) {
    ListUserOrder? lastPurchase = getLastValidPurchase(orders);
    if (lastPurchase == null) return;
    ShippingData? shippingData = lastPurchase.shippingData;

    if (shippingData?.address == null) return;

    final newAddress = Address(
      receiverName: shippingData!.address!.receiverName,
      postalCode: shippingData.address!.postalCode,
      city: shippingData.address!.city,
      state: shippingData.address!.state,
      street: shippingData.address!.street,
      number: shippingData.address!.number,
      neighborhood: shippingData.address!.neighborhood,
      complement: shippingData.address!.complement,
      country: shippingData.address!.country,
      geoCoordinates: shippingData.address!.geoCoordinates
              ?.toList()
              .map((c) => c.toDouble())
              .toList() ??
          [],
    );
    if (newAddress != addressInfo.value) {
      addressInfo.value = newAddress;
    }
  }

  ListUserOrder? getLastValidPurchase(List<ListUserOrder>? orders) {
    if (orders == null || orders.isEmpty) return null;
    return orders.firstWhere((order) => validAddressLastPurchase(order));
  }

  bool validAddressLastPurchase(ListUserOrder order) {
    return order.shippingData != null &&
        order.shippingData!.address != null &&
        order.shippingData!.address!.addressType != "pickup" &&
        order.shippingData!.address!.postalCode != null &&
        order.shippingData!.address!.postalCode != "";
  }

  bool get hasPostalCode {
    return addressInfo.value?.postalCode != null &&
        addressInfo.value!.postalCode!.isNotEmpty;
  }

  void updateControllerWithAddress(Address address) {
    addressInfo.value = address;
  }

  String get neighborhoodAndCity {
    Address? address = addressInfo.value;
    return [address?.neighborhood, address?.city]
        .where((e) => e != null && e.isNotEmpty)
        .join(', ');
  }
}

class InvalidCepException extends SomaCoreException {
  InvalidCepException(String message, {Object? cause})
      : super(message: message, cause: cause);
}

extension on List<ItemDeliveryInfo> {
  List<checkout_log.LogisticsInfo> toLogisticsInfo() {
    return map((s) {
      return checkout_log.LogisticsInfo(
        itemIndex: s.itemIndex,
        selectedSla: s.slaId,
        selectedDeliveryChannel: s.deliveryChannel,
      );
    }).toList();
  }

  String? get shippingTier {
    return firstWhereOrNull((s) => s.pickupStore?.isPickupStore ?? false)
            ?.pickupStore
            ?.name ??
        firstOrNull?.name;
  }
}

extension on SelectedDelivery {
  bool isInOptions(ShippingOptions shippingOptions, bool showAllOptions) {
    List<optionmodel.Options> deliveryOptions = <optionmodel.Options>[
      if (shippingOptions.economic != null) shippingOptions.economic!,
      if (shippingOptions.fast != null) shippingOptions.fast!,
    ];

    if (showAllOptions && shippingOptions.allDeliveryOptions != null) {
      deliveryOptions = shippingOptions.allDeliveryOptions!;
    }

    final deliveryItems = slas.where((s) => s.isDelivery).toList();
    final pickupItems = slas.where((s) => s.isPickupInPoint).toList();

    final allDeliveryItemsAreInOptions = deliveryOptions.any((o) =>
        deliveryItems.every((i) => o.slas.any((s) => i.isSameDelivery(s))));
    final allPickupItemsAreInOptions = pickupItems.every((i) =>
        shippingOptions.pickupInPoint?.slas.any((s) => i.isSameDelivery(s)) ??
        false);
    return allDeliveryItemsAreInOptions && allPickupItemsAreInOptions;
  }
}

class PackageModel {
  final List<String> items;
  final List<Slas> deliveryOptions;
  Slas deliveryOptionSelected;
  bool allItemsSameDelivery;
  List<String> imagesUrl;
  int price;
  bool isPickup;

  PackageModel({
    required this.items,
    required this.deliveryOptions,
    required this.allItemsSameDelivery,
    required this.deliveryOptionSelected,
    required this.imagesUrl,
    this.price = 0,
    this.isPickup = false,
  });

  void addItem(String item, int price) {
    items.add(item);
    this.price += price;
  }
}

abstract class DeliveryOption {
  final String id;
  final bool isSelected;
  final bool isDisabled;

  const DeliveryOption(this.id, this.isSelected, this.isDisabled);
}

class CommonDeliveryOption extends DeliveryOption {
  /// Preço da entrega
  ///
  /// Exemplo: "R\$ 10,00"
  final String deliveryPrice;

  /// Título da entrega
  ///
  /// Exemplo: "Entrega em até 3 dias úteis"
  final String deliveryTitle;

  /// Tempo estimado de entrega
  ///
  /// Exemplo: "3 dias úteis"
  final String deliveryTime;

  /// Cria uma opção de entrega comum
  ///
  /// Os parâmetros [id], [deliveryPrice], [deliveryTitle] e [deliveryTime] são obrigatórios
  CommonDeliveryOption({
    required String id,
    required this.deliveryPrice,
    required this.deliveryTitle,
    required this.deliveryTime,
    bool isSelected = false,
    bool isDisabled = false,
  }) : super(id, isSelected, isDisabled);
}
