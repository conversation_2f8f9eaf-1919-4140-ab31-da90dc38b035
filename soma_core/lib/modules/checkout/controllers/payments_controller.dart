import 'dart:async';
import 'dart:typed_data';

import 'package:adyen_checkout/adyen_checkout.dart';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:soma_core/modules/checkout/models/createPaymentData/trasaction_payment_apple_pay..dart';
import 'package:soma_core/shared/models/payment_system_ids.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_analytics/soma_analytics.dart';

import '../../../utils/credit_card_utils.dart';
import '../../auth/models/user_orders/gift_card.dart';
import '../../auth/models/user_orders/gift_card_collection.dart';
import '../models/GenerateCodePix/generate_code_pix.dart';
import '../models/address_shipping_option_request/address.dart';
import '../models/createOrder/create_order.dart';
import '../models/createPaymentData/transaction_payment.dart';
import '../models/createPaymentData/transaction_payment_credit_card.dart';
import '../models/createPaymentData/transaction_payment_credit_card_fields.dart';
import '../models/createPaymentData/transaction_payment_gift_card.dart';
import '../models/createPaymentData/transaction_payment_pix.dart';
import '../models/createPaymentData/transaction_payment_transaction.dart';
import '../models/index.dart';
import '../models/models/status_code_pix.dart';
import '../models/orderForm/available_accounts.dart';
import '../services/payments_services.dart';
import '../utils/message_error_order_form.dart';
import '../utils/update_order_form.dart';
import 'cashback_controller.dart';
import 'credit_voucher_controller.dart';
import '../models/apple_pay/apple_pay.dart';
import '../models/apple_pay/apple_pay_model.dart' as adyen;
// import '../models/apple_pay/payments_request_data.dart';

class NewCreditCard {
  final String cardNumber;
  final String name;
  final String expiryDate;
  final String cvv;
  final String cpf;
  final Address? address;

  const NewCreditCard({
    required this.cardNumber,
    required this.name,
    required this.expiryDate,
    required this.cvv,
    required this.cpf,
    this.address,
  });
}

class PaymentSelected {
  PaymentMethodOptions? paymentMethod;
  AvailableAccounts? recurrentCreditcard;
  NewCreditCard? newCreditCard;
  bool? cardIsNew;
  String? cardName;
  String? cardFinalNumber;
  GiftCardCollection? giftCard;
  String? cvv;
  String paymentSystemId;

  PaymentSelected({
    this.paymentSystemId = '',
    this.paymentMethod,
    this.recurrentCreditcard,
    this.cardIsNew,
    this.newCreditCard,
    this.cardFinalNumber,
    this.cardName,
    this.giftCard,
    this.cvv,
  });

  factory PaymentSelected.fromRegisteredCreditCard(
      AvailableAccounts creditCard) {
    return PaymentSelected(
      paymentMethod: PaymentMethodOptions.creditCard,
      recurrentCreditcard: creditCard,
      cardIsNew: false,
      cardFinalNumber: creditCard.finalNumber,
      cardName: creditCard.paymentSystemName.toLowerCase(),
      paymentSystemId: creditCard.paymentSystem,
    );
  }

  bool get isIncomplete {
    if (paymentMethod == PaymentMethodOptions.creditCard) {
      return cardFinalNumber == null ||
          cardName == null ||
          (cardIsNew == true && newCreditCard?.cvv == null);
    } else if (paymentMethod == PaymentMethodOptions.giftCard) {
      return giftCard?.isEmpty ?? true;
    }
    return paymentSystemId.isEmpty;
  }
}

class PaymentsController extends GetxController with UpdateOrderForm {
  final PaymentsService service;
  final Rxn<OrderForm> orderForm;
  final SomaAnalyticsController somaAnalyticsController;
  final CashBackController cashBackController;
  final CreditVoucherController creditVoucherController;
  final RecaptchaChecker recaptchaChecker;
  @override
  final KeyValueStorage storage;
  final OrderFormUtils orderFormUtils;
  final Config config;

  Rxn<bool> isLoading = Rxn<bool>(false);
  Rxn<bool> isLoadingPaymentSelect = Rxn<bool>(false);
  Rxn<bool> hasErrorWithPayment = Rxn<bool>(false);

  // CreditCardPaymentGroup? paymentSystem;
  String? paymentSystemId;
  Rxn<InstallmentOrderForm>? installment = Rxn<InstallmentOrderForm>(null);
  Rxn<adyen.ApplePayModel>? applePayModel = Rxn<adyen.ApplePayModel>(null);
  Rxn<ApplePayConfig> applePayConfig = Rxn<ApplePayConfig>(null);
  Rxn<PaymentSelected> paymentSelected = Rxn<PaymentSelected>(null);
  CancelToken statusPixCancelToken = CancelToken();

  String transactionId = '';
  String orderGroup = '';
  String merchantName = '';
  String code = '';
  late Uint8List qrCodeBase64Image;
  String paymentId = '';
  String urlPix = '';

  PaymentsController(
    this.service,
    this.orderForm,
    this.somaAnalyticsController,
    this.cashBackController,
    this.creditVoucherController,
    this.storage,
    this.recaptchaChecker,
    this.orderFormUtils,
    this.config,
  );

  Map paymentMethodOptions = {
    PaymentMethodOptions.pix: 'instantPaymentPaymentGroup',
    PaymentMethodOptions.creditCard: 'creditCardPaymentGroup',
    PaymentMethodOptions.giftCard: 'VtexGiftCard',
  };

  Map<dynamic, dynamic> get payamentMethods => paymentMethodOptions;

  bool selectedPaymentMethodIsAccepted() {
    final paymentMethod = paymentSelected.value?.paymentMethod;
    final orderForm = this.orderForm.value;
    return paymentMethod != null &&
        (orderForm?.acceptsPaymentMethod(paymentMethod) ?? false);
  }

  String getInstallmentCount() {
    if (installment != null &&
        installment!.value != null &&
        installment!.value!.count != null) {
      return installment!.value!.count.toString();
    }
    return "1"; //PIX;
  }

  int getInstallmentValue() {
    if (installment != null && installment!.value != null) {
      if (installment!.value!.value != null) {
        return installment!.value!.value!; //Parcelamento
      } else {
        return installment!.value!.total!; //Valor total / PIX
      }
    }
    return 0;
  }

  bool validateCreditCard({required String creditCard}) {
    var cardIsValid =
        CreditCardUtils.validateCardByflag(cardNumber: creditCard);
    if (cardIsValid) {
      return true;
    } else {
      return false;
    }
  }

  List<InstallmentOrderForm> getInstallments() {
    var installmentOptions = orderForm.value!.paymentData!.installmentOptions!
        .map((s) => InstallmentsOptions.fromMap(s))
        .toList();

    var installmentsByPaymentSystem = installmentOptions
        .where((element) =>
            element.paymentSystem == paymentSelected.value?.paymentSystemId)
        .toList()
        .first;

    List<InstallmentOrderForm> installments = [];

    for (var element in installmentsByPaymentSystem.installments!) {
      if (element.value != null) {
        installments.add(
          element,
        );
      }
    }
    return installments;
  }

  Future<OrderForm> _paymentSelect(
      {required PaymentSelect paymentMethod}) async {
    try {
      isLoadingPaymentSelect(true);
      if (orderForm.value!.orderFormId != null) {
        var response = await service.paymentSelect(
          orderFormId: this.orderForm.value!.orderFormId!,
          paymentSelect: paymentMethod,
        );

        this.orderForm(response);
        updateOrder(response);

        if (paymentMethod.payments != null) {
          var paymentSystemId =
              paymentMethod.payments?.firstOrNull?.paymentSystem;
          if (paymentSystemId != null) {
            var creditCardName =
                CreditCardUtils.getNameBypaymentSystem(paymentSystemId);
            paymentSelected.value?.cardName = creditCardName;
          }
        }

        final orderForm = this.orderForm.value;

        if (orderFormUtils.getGiftCardValues(orderForm!) >=
            orderForm.value!.toDouble()) {
          // if (paymentSelected.value == null || paymentSelected.value?.paymentMethod == null) {
          paymentSelected.value =
              PaymentSelected(paymentMethod: PaymentMethodOptions.giftCard);
          // } else {
          //   paymentSelected.value?.paymentMethod =
          //       PaymentMethodOptions.giftCard;
          // }
          //

          paymentSelected.refresh();
        } else if (orderFormUtils.getGiftCardValues(orderForm) == 0 &&
            paymentSelected.value?.paymentMethod ==
                PaymentMethodOptions.giftCard) {
          paymentSelected.value?.paymentMethod = null;
        } else if (orderForm.firstSelectedPayment != null) {
          final firstSelectedPaymentSystem =
              orderForm.firstSelectedPayment?.paymentSystem;

          if (firstSelectedPaymentSystem == PaymentSystemIds.applePay) {
            paymentSelected.value!.paymentMethod =
                PaymentMethodOptions.applePay;
            paymentSelected.value!.paymentSystemId = PaymentSystemIds.applePay;
          } else if (firstSelectedPaymentSystem == PaymentSystemIds.pix) {
            paymentSelected.value?.paymentMethod = PaymentMethodOptions.pix;
            paymentSelected.value?.paymentSystemId = PaymentSystemIds.pix;
          } else if (firstSelectedPaymentSystem ==
              PaymentSystemIds.pixInstallments) {
            paymentSelected.value?.paymentMethod =
                PaymentMethodOptions.pixInstallments;
            paymentSelected.value?.paymentSystemId =
                PaymentSystemIds.pixInstallments;
          } else if (paymentSelected.value == null &&
              firstSelectedPaymentSystem != "" &&
              orderFormUtils.getGiftCardValues(orderForm) <
                  orderForm.value!.toDouble()) {
            paymentSelected.value?.paymentMethod = null;
          } else if (PaymentSystemIds.creditCardSystem.any(
              (element) => element == paymentSelected.value!.paymentSystemId)) {
            paymentSelected.value!.paymentMethod =
                PaymentMethodOptions.creditCard;
          }
        }

        return response;
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoadingPaymentSelect(false);
    }
  }

  Future<void> _installmentSelectNewCreditCrad() async {
    var cardNumber = paymentSelected.value?.newCreditCard?.cardNumber;
    var cardFinalNumber = cardNumber?.substring(cardNumber.length - 4);
    paymentSelected.value?.cardFinalNumber = cardFinalNumber;

    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
              paymentSystem: paymentSelected.value?.paymentSystemId,
              installments: installment!.value!.count,
              referenceValue: installment!.value!.total,
              value: installment!.value!.value)
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm.value!),
      ),
    );
  }

  Future<void> __installmentSelectRecurrentCreditcard() async {
    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
            accountId: paymentSelected.value?.recurrentCreditcard?.accountId,
            bin: paymentSelected.value?.recurrentCreditcard?.bin,
            paymentSystem:
                paymentSelected.value?.recurrentCreditcard?.paymentSystem,
            installments: installment!.value!.count,
            referenceValue: installment!.value!.total,
            value: installment!.value!.value,
          ),
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm.value!),
      ),
    );
  }

  Future<void> _paymentSelectNewCreditcard() async {
    final orderForm = this.orderForm.value!;
    var params = PaymentSelect(
      payments: [
        PaymentPaymentSelect(
          paymentSystem: paymentSelected.value?.paymentSystemId,
          installments: 1,
          installmentsInterestRate: 0,
          installmentsValue: orderFormUtils.getValue(orderForm),
          value: orderFormUtils.getValue(orderForm),
          referenceValue: orderFormUtils.getValue(orderForm),
        ),
      ],
      giftCards: orderFormUtils.getGiftCardsInUse(orderForm),
    );

    await _paymentSelect(paymentMethod: params);
  }

  Future<void> _paymentSelectRecurrentCreditcard() async {
    final orderForm = this.orderForm.value!;
    final firstValidInstallment = getFirstValidInstallment();
    if (installment?.value == null ||
        installment?.value?.count == null ||
        installment?.value?.value == null) {
      installment?.value = InstallmentOrderForm(
        count: firstValidInstallment?.count ?? 1,
        value:
            firstValidInstallment?.value ?? orderFormUtils.getValue(orderForm),
      );
    }

    var cardNumber = paymentSelected.value?.recurrentCreditcard?.cardNumber;
    var cardFinalNumber = cardNumber?.substring(cardNumber.length - 4);
    paymentSelected.value?.cardFinalNumber = cardFinalNumber;

    paymentSelected.value?.paymentSystemId =
        paymentSelected.value?.recurrentCreditcard?.paymentSystem ?? '';

    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
            accountId: paymentSelected.value?.recurrentCreditcard?.accountId,
            bin: paymentSelected.value?.recurrentCreditcard?.bin,
            paymentSystem:
                paymentSelected.value?.recurrentCreditcard?.paymentSystem,
            installments: firstValidInstallment?.count ?? 1,
            installmentsValue: firstValidInstallment?.value ??
                orderFormUtils.getValue(orderForm),
            referenceValue: orderFormUtils.getValue(orderForm),
            value: orderFormUtils.getValue(orderForm),
          ),
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm),
      ),
    );

    installment?.value = firstValidInstallment;
  }

  Future<void> _paymentSelectApplePay() async {
    final orderForm = this.orderForm.value!;
    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
            paymentSystem: PaymentSystemIds.applePay,
            installments: 1,
            installmentsInterestRate: 0,
            installmentsValue: orderFormUtils.getValue(orderForm),
            value: orderFormUtils.getValue(orderForm),
            referenceValue: orderFormUtils.getValue(orderForm),
          ),
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm),
      ),
    );
  }

  Future<void> _paymentSelectPix() async {
    final orderForm = this.orderForm.value!;
    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
            paymentSystem: PaymentSystemIds.pix,
            installments: 1,
            referenceValue: orderFormUtils.getValue(orderForm),
            value: orderFormUtils.getValue(orderForm),
          ),
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm),
      ),
    );
  }

  Future<void> paymentSelect() async {
    if (paymentSelected.value?.paymentMethod == null) return;

    if (paymentSelected.value?.paymentMethod == PaymentMethodOptions.pix) {
      return await _paymentSelectPix();
    }

    if (paymentSelected.value?.paymentMethod == PaymentMethodOptions.applePay) {
      return await _paymentSelectApplePay();
    }

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.pixInstallments) {
      return await _paymentSelectPixInstallments();
    }

    if (paymentSelected.value?.cardIsNew == false) {
      return _paymentSelectRecurrentCreditcard();
    } else {
      return _paymentSelectNewCreditcard();
    }
  }

  Future<void> installmentSelect() async {
    if (paymentSelected.value?.cardIsNew == true) {
      return _installmentSelectNewCreditCrad();
    } else {
      return __installmentSelectRecurrentCreditcard();
    }
  }

  Future<CreateOrder> _createOrder() async {
    try {
      isLoading(true);
      if (orderForm.value!.orderFormId != null) {
        final recaptchaResult = await recaptchaChecker.check('pix_transaction');
        var response = await service.createOrder(
          orderFormId: orderForm.value!.orderFormId!,
          value: int.parse(orderForm.value!.value!.toString()),
          interestValue: 0,
          recaptchaKey: recaptchaResult.key,
          recaptchaToken: recaptchaResult.token,
        );
        return response;
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } on DioException catch (e) {
      if (e.response?.data['error']['code'].contains('CHK0328')) {
        throw SomaCoreExceptionPaymentPixInvalid(
          cause: e,
        );
      }
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  List<TransactionPaymentGiftCard> getGiftcardsTransaction(
      TransactionPaymentTransaction? transaction) {
    List<TransactionPaymentGiftCard> giftCards = [];

    final orderForm = this.orderForm.value;
    if (orderForm != null &&
        orderFormUtils.getGiftCardsInUse(orderForm).isNotEmpty) {
      for (var item in orderFormUtils.getGiftCardsInUse(orderForm)) {
        giftCards.add(TransactionPaymentGiftCard(
            paymentSystem: PaymentSystemIds.giftCard,
            value: item.value ?? 0,
            referenceValue: item.value ?? 0,
            installmentsValue: item.value ?? 0,
            installments: 1,
            fields: item,
            transaction: transaction));
      }
    }

    return giftCards;
  }

  Future<void> _creditCardTransaction() async {
    try {
      isLoading(true);
      if (orderForm.value?.orderFormId != null) {
        var payment = orderForm.value!.firstSelectedPayment;
        List<TransactionPayment> params = [];
        var giftCards = getGiftcardsTransaction(null);
        TransactionPaymentCreditCardFields fields;
        if (giftCards.isNotEmpty) {
          params.add(TransactionPayment(giftCard: giftCards));
        }
        for (final merchantSellerPayment
            in orderForm.value!.merchantSellerPayments) {
          TransactionPaymentCreditCard? creditCard;
          if (payment != null) {
            if (paymentSelected.value!.cardIsNew == true) {
              final creditCard = paymentSelected.value!.newCreditCard!;
              fields = TransactionPaymentCreditCardFields(
                cardNumber: creditCard.cardNumber,
                holderName: creditCard.name,
                validationCode: creditCard.cvv,
                dueDate: creditCard.expiryDate,
                address: creditCard.address,
                addressId: creditCard.address == null
                    ? orderForm.value!.shippingData!.selectedAddresses!.first
                        .addressId!
                    : null,
              );
            } else {
              fields = TransactionPaymentCreditCardFields(
                  bin: paymentSelected.value?.recurrentCreditcard?.bin,
                  accountId:
                      paymentSelected.value?.recurrentCreditcard?.accountId,
                  addressId: orderForm
                      .value?.shippingData?.selectedAddresses?.first.addressId,
                  validationCode: paymentSelected.value!.cvv);
            }
            creditCard = TransactionPaymentCreditCard(
                paymentSystem: payment.paymentSystem,
                value: merchantSellerPayment.value,
                referenceValue: merchantSellerPayment.referenceValue,
                interestValue: 0,
                hasDefaultBillingAddress: true,
                installmentsInterestRate: 0,
                fields: fields,
                isBillingAddressDifferent: false,
                // condição criada por conta de um bug que a compra a vista estava passando o parcelamento 0
                installments: merchantSellerPayment.installments == 0
                    ? 1
                    : merchantSellerPayment.installments,
                interestRate: 0,
                installmentValue: merchantSellerPayment.installmentValue,
                chooseToUseNewCard:
                    paymentSelected.value!.cardIsNew == true ? true : false,
                currencyCode: 'BRL',
                originalPaymentIndex: 0,
                groupName: 'creditCardPaymentGroup');
          }
          params.add(TransactionPayment(creditCard: creditCard));
        }
        final recaptchaResult = await recaptchaChecker.check(
          'credit_card_transaction',
        );
        var response = await service.paymentTransaction(
          orderFormId: orderForm.value!.orderFormId!,
          transactionPaymentaymentSelect: params,
          recaptchaKey: recaptchaResult.key,
          recaptchaToken: recaptchaResult.token,
        );

        if (response.isNotEmpty) {
          orderGroup = response[0];
          transactionId = response[1];
        }
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<void> paymentCreditCard() async {
    try {
      await _creditCardTransaction();
    } catch (e) {
      if (e is SomaCoreException) {
        rethrow;
      }
      throw SomaCoreException(message: e.toString(), cause: e);
    }
  }

  Future<adyen.ApplePayModel?> paymentApplePay() async {
    try {
      return await _applePayTransaction();
    } catch (e) {
      if (e is SomaCoreException) {
        rethrow;
      }
      throw SomaCoreException(message: e.toString(), cause: e);
    }
  }

  Future<void> _pixTransaction() async {
    try {
      isLoading(true);

      if (orderForm.value!.firstSelectedPayment != null) {
        var transaction = TransactionPaymentTransaction(
            id: transactionId, merchantName: merchantName);
        var payment = orderForm.value!.firstSelectedPayment;

        var pix = TransactionPaymentPix(
          paymentSystem: payment?.paymentSystem ?? '',
          value: payment?.value ?? 0,
          referenceValue: payment?.referenceValue ?? 0,
          installmentValue:
              payment?.merchantSellerPayments.first.installmentValue ?? 0,
          transaction: transaction,
          paymentSystemName: payment?.paymentSystem ?? '',
          id: payment?.merchantSellerPayments.first.id ?? '',
        );

        var giftCards = getGiftcardsTransaction(transaction);

        var params = TransactionPayment(pix: pix, giftCard: giftCards);

        await service.pixTransaction(
            orderId: orderGroup,
            transactionId: transactionId,
            transactionPaymentaymentSelect: params);
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<GenerateCodePix> _generateCodePixSecondCall(
      {required String orderGroupID}) async {
    try {
      isLoading(true);
      if (orderForm.value!.orderFormId != null) {
        return await service.generateCodePixSecondCall(
            orderGroupID: orderGroupID);
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<StatusCodePix> statusCodePix(
      {required String transactionId,
      required String paymentId,
      OrderForm? orderForm}) async {
    try {
      if (orderForm?.orderFormId != null) {
        if (statusPixCancelToken.isCancelled) {
          statusPixCancelToken = CancelToken();
        }

        final statusCodePix = await service.statusCodePix(
            transactionId: transactionId,
            paymentId: paymentId,
            cancelToken: statusPixCancelToken);

        if (statusCodePix.authorizationDate != null && orderForm != null) {
          await somaAnalyticsController.logPurchase(
            screenClass: 'PixPage',
            orderForm: orderForm,
            transactionId: orderGroup,
            paymentType: paymentSelected.value?.paymentMethod.toString(),
          );
        }

        return statusCodePix;
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    }
  }

  Future<void> paymentPix() async {
    var response = await _createOrder();
    transactionId = response.merchantTransactions![0].transactionId!;
    orderGroup = response.orderGroup!;
    merchantName = response.merchantTransactions![0].merchantName!;

    await _pixTransaction();
    var token = await _generateCodePixSecondCall(orderGroupID: orderGroup);

    final url = token.paymentAuthorizationAppCollection?[0].appPayload;

    final isPixInstallments = paymentSelected.value?.paymentSystemId ==
        PaymentSystemIds.pixInstallments;

    if (isPixInstallments && url != null) {
      urlPix = '$url&cd=/cancel';
    } else {
      urlPix = url ?? '';
    }

    //RETORNO DO PIX ANTIGO
    // String base64Image = toJson.qrCodeBase64Image;
    // if (toJson.qrCodeBase64Image.contains(':')) {
    //   base64Image = toJson.qrCodeBase64Image.split(':')[1].split(',')[1];
    // }
    // if (toJson.qrCodeBase64Image.contains('\n')) {
    //   base64Image = toJson.qrCodeBase64Image.replaceAll('\n', '');
    // }

    // var bytes = base64Decode(base64Image);

    // code = toJson.code;
    // qrCodeBase64Image = bytes;
    // paymentId = toJson.paymentId;
  }

  PaymentPaymentSelect? _getPaymentSelected() {
    if (orderForm.value != null &&
        orderForm.value!.firstSelectedPayment != null) {
      int installmentTotalValue = 0;
      if ((orderForm.value?.merchantSellerPayments.length ?? 0) > 0) {
        installmentTotalValue = orderForm.value?.merchantSellerPayments
                .map((e) => e.installmentValue)
                .reduce((p, e) => p + e) ??
            0;
      }
      return PaymentPaymentSelect(
          paymentSystem: orderForm.value!.firstSelectedPayment?.paymentSystem,
          bin: orderForm.value!.firstSelectedPayment?.bin,
          accountId: orderForm.value!.firstSelectedPayment?.accountId,
          tokenId: orderForm.value!.firstSelectedPayment?.tokenId,
          installments: orderForm.value!.firstSelectedPayment?.installments,
          installmentsValue: installmentTotalValue,
          referenceValue: orderForm.value!.firstSelectedPayment?.referenceValue,
          value: orderForm.value!.firstSelectedPayment?.value);
    }

    return null;
  }

  Future<void> deleteGiftCard({required GiftCard giftCard}) async {
    List<GiftCard> giftCards = [];
    var payment = _getPaymentSelected();

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      final firstValidInstallment = getFirstValidInstallment();

      payment?.installments = firstValidInstallment?.count ?? 1;
      payment?.installmentsValue = firstValidInstallment?.value ??
          orderFormUtils.getValue(orderForm.value!);

      installment?.value = firstValidInstallment;
    }

    if (orderForm.value?.paymentData != null &&
        orderForm.value?.paymentData!.giftCards != null) {
      for (var item in orderForm.value!.paymentData!.giftCards!) {
        if (item.redemptionCode != null &&
            item.redemptionCode != giftCard.redemptionCode) {
          if (config.appFeaturesConfig.giftCardConfig?.useMultipleGiftCards ??
              true) {
            item.value = null;
            item.inUse = false;
          }

          giftCards.add(item);
        }
      }
    }

    await _paymentSelect(
        paymentMethod: PaymentSelect(
            giftCards: giftCards,
            payments: payment != null ? [payment] : null));

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      installment?.value = getFirstValidInstallment();
    }
  }

  Future<void> addGiftCard(
      {required GiftCard giftCard, required bool isSpecialCard}) async {
    List<GiftCard> giftCards = [];
    giftCards.add(giftCard);
    var payment = _getPaymentSelected();

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      final firstValidInstallment = getFirstValidInstallment();

      payment?.installments = firstValidInstallment?.count ?? 1;
      payment?.installmentsValue = firstValidInstallment?.value ??
          orderFormUtils.getValue(orderForm.value!);

      installment?.value = firstValidInstallment;
    }

    if (orderForm.value!.paymentData != null &&
        orderForm.value!.paymentData!.giftCards != null) {
      for (var item in orderForm.value!.paymentData!.giftCards!) {
        if (item.redemptionCode != giftCard.redemptionCode) {
          if (config.appFeaturesConfig.giftCardConfig?.useMultipleGiftCards ??
              true) {
            item.value = giftCard.value;
            item.inUse = true;
          }

          giftCards.add(item);
        }
      }
    }

    var response = await _paymentSelect(
        paymentMethod: PaymentSelect(
            giftCards: giftCards,
            payments: payment != null ? [payment] : null));

    if (response.messages!.isNotEmpty) {
      if (response.messages?.first.code == 'noFundsGiftCard' ||
          response.messages?.first.code == 'giftCardCommunicationError' ||
          response.messages?.first.code == 'invalidGiftCard' &&
              response.messages?.first.status == 'error') {
        throw SomaCoreExceptionGiftCardInvalid();
      }
    }

    if (isSpecialCard) {
      await _paymentSelect(
          paymentMethod: PaymentSelect(
        giftCards: response.paymentData?.giftCards,
        payments: response.paymentData?.payments
            ?.map((p) => PaymentPaymentSelect().fromPaymentOrderForm(p))
            .toList(),
      ));
    }

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      installment?.value = getFirstValidInstallment();
    }
  }

  Future<void> addGiftCardCollection(
      {required GiftCardCollection collection,
      required bool isSpecialCard}) async {
    List<GiftCard> giftCards = collection.calculateUsage();
    var payment = _getPaymentSelected();

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      final firstValidInstallment = getFirstValidInstallment();

      payment?.installments = firstValidInstallment?.count ?? 1;
      payment?.installmentsValue = firstValidInstallment?.value ??
          orderFormUtils.getValue(orderForm.value!);

      installment?.value = firstValidInstallment;
    }

    if (orderForm.value!.paymentData != null &&
        orderForm.value!.paymentData!.giftCards != null) {
      for (var item in orderForm.value!.paymentData!.giftCards!) {
        if (!collection.contains(item.giftCardId)) {
          giftCards.add(item);
        }
      }
    }

    var response = await _paymentSelect(
        paymentMethod: PaymentSelect(
            giftCards: giftCards,
            payments: payment != null ? [payment] : null));

    if (response.messages!.isNotEmpty) {
      if (response.messages?.first.code == 'noFundsGiftCard' ||
          response.messages?.first.code == 'giftCardCommunicationError' ||
          response.messages?.first.code == 'invalidGiftCard' &&
              response.messages?.first.status == 'error') {
        throw SomaCoreExceptionGiftCardInvalid();
      }
    }

    if (isSpecialCard) {
      await _paymentSelect(
          paymentMethod: PaymentSelect(
        giftCards: response.paymentData?.giftCards,
        payments: response.paymentData?.payments
            ?.map((p) => PaymentPaymentSelect().fromPaymentOrderForm(p))
            .toList(),
      ));
    }

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      installment?.value = getFirstValidInstallment();
    }
  }

  Future<void> deleteGiftCardCollection(
      {required GiftCardCollection collection}) async {
    List<GiftCard> giftCards = orderForm.value?.paymentData?.giftCards ?? [];
    var payment = _getPaymentSelected();

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      final firstValidInstallment = getFirstValidInstallment();

      payment?.installments = firstValidInstallment?.count ?? 1;
      payment?.installmentsValue = firstValidInstallment?.value ??
          orderFormUtils.getValue(orderForm.value!);

      installment?.value = firstValidInstallment;
    }

    await _paymentSelect(
        paymentMethod: PaymentSelect(
            giftCards: giftCards
                .whereNot((g) => collection.contains(g.giftCardId!))
                .toList(),
            payments: payment != null ? [payment] : null));

    if (paymentSelected.value?.paymentMethod ==
        PaymentMethodOptions.creditCard) {
      installment?.value = getFirstValidInstallment();
    }
  }

  Future<void> _paymentSelectPixInstallments() async {
    final orderForm = this.orderForm.value!;
    await _paymentSelect(
      paymentMethod: PaymentSelect(
        payments: [
          PaymentPaymentSelect(
            paymentSystem: PaymentSystemIds.pixInstallments,
            installments: 1,
            referenceValue: orderFormUtils.getValue(orderForm),
            value: orderFormUtils.getValue(orderForm),
          ),
        ],
        giftCards: orderFormUtils.getGiftCardsInUse(orderForm),
      ),
    );
  }

  InstallmentOrderForm? getFirstValidInstallment() {
    return orderForm.value!.getInstallmentOptions
        .firstWhereOrNull((o) =>
            o.paymentSystem ==
                paymentSelected.value?.recurrentCreditcard?.paymentSystem ||
            o.paymentSystem == paymentSelected.value?.paymentSystemId)
        ?.installments
        ?.firstOrNull;
  }

  String get getCurrentInstallmentReview {
    try {
      if (paymentSelected.value?.paymentMethod == PaymentMethodOptions.pix ||
          installment!.value?.total != orderForm.value?.value) {
        return orderForm.value?.getCurrentInstallment ?? '';
      }

      return orderForm.value?.getCurrentInstallment ?? '';
    } catch (_) {
      return '1 x de R\$ 0,00';
    }
  }

  Future<adyen.ApplePayModel?> _applePayTransaction() async {
    try {
      isLoading(true);
      if (orderForm.value?.orderFormId != null) {
        final payment = orderForm.value!.firstSelectedPayment;
        List<TransactionPayment> params = [];
        final giftCards = getGiftcardsTransaction(null);
        if (giftCards.isNotEmpty) {
          params.add(TransactionPayment(giftCard: giftCards));
        }
        final applePay = TransactionPaymentApplePay(
          paymentSystem: payment?.paymentSystem ?? '',
          value: payment?.value ?? 0,
          referenceValue: payment?.referenceValue ?? 0,
          installments: 1,
          merchantSellerPayments: [
            MerchantSellerPayment(
              id: payment?.merchantSellerPayments.first.id ?? '',
              installments: 1,
              referenceValue: payment?.referenceValue ?? 0,
              value: payment?.value ?? 0,
              interestRate: 0,
              installmentValue:
                  payment?.merchantSellerPayments.first.installmentValue ?? 0,
            )
          ],
        );
        params.add(TransactionPayment(
          applePay: applePay,
        ));
        final recaptchaResult = await recaptchaChecker.check(
          'apple_pay_transaction',
        );

        var response = await service.paymentApplePayTransaction(
          orderFormId: orderForm.value!.orderFormId!,
          transactionPaymentaymentSelect: params,
          recaptchaKey: recaptchaResult.key,
          recaptchaToken: recaptchaResult.token,
        );

        ///TODO: Quando implementarmos outras marcas, o backend precisará enviar o número do pedido já formatado para cada marca.
        orderGroup = 'v${response?.orderId ?? ''}anm';

        return response;
      } else {
        throw SomaCoreException(message: MessageErrorOrderForm.orderFormIsNull);
      }
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    } finally {
      isLoading(false);
    }
  }

  Future<PaymentEvent> onSubmit(
    Map<String, dynamic> data, [
    Map<String, dynamic>? extra,
  ]) async {
    final String? applePayToken = data['paymentMethod']?['applePayToken'];

    final response = await service.postPayments(
      applePayModel?.value?.paymentId ?? '',
      applePayToken ?? '',
    );
    final PaymentEventHandler paymentEventHandler = PaymentEventHandler();
    return paymentEventHandler.handleResponse(response);
  }

  Future<PaymentEvent> onAdditionalDetailsMock(
          Map<String, dynamic> additionalDetailsJson) =>
      Future.error(
          "Additional details call is not required for the Apple Pay component.");

  Future<void> userCancelApplePay(
    String paymentId,
  ) async {
    try {
      await service.postUserCancel(paymentId);
    } catch (e) {
      throw SomaCoreException(message: e.toString(), cause: e);
    }
  }
}
