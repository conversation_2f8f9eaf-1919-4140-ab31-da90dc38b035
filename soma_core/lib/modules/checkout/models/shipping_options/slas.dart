import 'package:collection/collection.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/options.dart';

import '../../../../soma_core.dart';
import 'business_hour.dart';
import 'delivery_id.dart';
import 'pickup_store_info.dart';
import 'shipping_estimate_mixin.dart';

class Slas with PickupStore, ShippingEstimateMixin {
  @override
  String? id;
  @override
  String? deliveryChannel;
  @override
  String? name;
  List<DeliveryId>? deliveryIds;
  @override
  String? shippingEstimate;
  dynamic shippingEstimateDate;
  dynamic lockTtl;
  List<dynamic>? availableDeliveryWindows;
  dynamic deliveryWindow;
  int? price;
  int? listPrice;
  int? tax;
  PickupStoreInfo? pickupStoreInfo;
  @override
  dynamic pickupPointId;
  @override
  double? pickupDistance;
  String? polygonName;
  String? transitTime;
  List<String>? itemsId;

  Slas({
    this.id,
    this.deliveryChannel,
    this.name,
    this.deliveryIds,
    this.shippingEstimate,
    this.shippingEstimateDate,
    this.lockTtl,
    this.availableDeliveryWindows,
    this.deliveryWindow,
    this.price,
    this.listPrice,
    this.tax,
    this.pickupStoreInfo,
    this.pickupPointId,
    this.pickupDistance,
    this.polygonName,
    this.transitTime,
    this.itemsId,
  });

  factory Slas.fromJson(Map<String, dynamic> json) {
    return Slas(
      id: json['id'] as String?,
      deliveryChannel: json['deliveryChannel'] as String?,
      name: json['name'] as String?,
      deliveryIds: (json['deliveryIds'] as List<dynamic>?)
          ?.map((e) => DeliveryId.fromJson(e as Map<String, dynamic>))
          .toList(),
      shippingEstimate: json['shippingEstimate'] as String?,
      shippingEstimateDate: json['shippingEstimateDate'] as dynamic,
      lockTtl: json['lockTTL'] as dynamic,
      availableDeliveryWindows:
          json['availableDeliveryWindows'] as List<dynamic>?,
      deliveryWindow: json['deliveryWindow'] as dynamic,
      price: json['price'] as int?,
      listPrice: json['listPrice'] as int?,
      tax: json['tax'] as int?,
      pickupStoreInfo: json['pickupStoreInfo'] == null
          ? null
          : PickupStoreInfo.fromJson(
              json['pickupStoreInfo'] as Map<String, dynamic>),
      pickupPointId: json['pickupPointId'] as dynamic,
      pickupDistance: (json['pickupDistance'] as num?)?.toDouble(),
      polygonName: json['polygonName'] as String?,
      transitTime: json['transitTime'] as String?,
      itemsId:
          (json['itemsId'] as List?)?.map((e) => e as String).toList() ?? [],
    );
  }

  bool get isPickupInPoint => deliveryChannel == 'pickup-in-point';

  @override
  bool? get isPickupStore => pickupStoreInfo?.isPickupStore;

  String? get shippingEstimatedFormatted {
    if (shippingEstimate == null) return null;
    // só utiliza o metodo definido no shippingOptions
    return OptionsNew.withShippingEstimate(shippingEstimate!)
        .getShippingEstimatedFormatted;
  }

  String get shippingPrice {
    if (price == 0) return 'grátis';

    return CurrencyUtils.format(amount: price, dividedBy100: true);
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'deliveryChannel': deliveryChannel,
        'name': name,
        'deliveryIds': deliveryIds?.map((e) => e.toJson()).toList(),
        'shippingEstimate': shippingEstimate,
        'shippingEstimateDate': shippingEstimateDate,
        'lockTTL': lockTtl,
        'availableDeliveryWindows': availableDeliveryWindows,
        'deliveryWindow': deliveryWindow,
        'price': price,
        'listPrice': listPrice,
        'tax': tax,
        'pickupStoreInfo': pickupStoreInfo?.toJson(),
        'pickupPointId': pickupPointId,
        'pickupDistance': pickupDistance,
        'polygonName': polygonName,
        'transitTime': transitTime,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! Slas) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      deliveryChannel.hashCode ^
      name.hashCode ^
      deliveryIds.hashCode ^
      shippingEstimate.hashCode ^
      shippingEstimateDate.hashCode ^
      lockTtl.hashCode ^
      availableDeliveryWindows.hashCode ^
      deliveryWindow.hashCode ^
      price.hashCode ^
      listPrice.hashCode ^
      tax.hashCode ^
      pickupStoreInfo.hashCode ^
      pickupPointId.hashCode ^
      pickupDistance.hashCode ^
      polygonName.hashCode ^
      transitTime.hashCode;

  @override
  StoreAddress? get address => StoreAddress(
        street: pickupStoreInfo?.address?.street,
        number: pickupStoreInfo?.address?.number,
        complement: pickupStoreInfo?.address?.complement,
        neighborhood: pickupStoreInfo?.address?.neighborhood,
        postalCode: pickupStoreInfo?.address?.postalCode,
        city: pickupStoreInfo?.address?.city,
        state: pickupStoreInfo?.address?.state,
        country: pickupStoreInfo?.address?.country,
        reference: pickupStoreInfo?.address?.reference,
        geoCoordinates: pickupStoreInfo?.address?.geoCoordinates,
        receiverName: pickupStoreInfo?.address?.receiverName,
      );

  @override
  List<BusinessHour>? get businessHours => pickupStoreInfo?.businessHours;

  @override
  String? get friendlyName => pickupStoreInfo?.friendlyName;

  String? get addressResume {
    return [
      address?.state,
      address?.street,
    ].joinNotEmpty(' - ');
  }

  double? get distance => pickupDistance;

  String? get fullAddress {
    return [
      [
        address?.street,
        address?.number,
      ].joinNotEmpty(', '),
      [
        address?.neighborhood,
        address?.city,
      ].joinNotEmpty(', '),
      [
        address?.state,
        address?.postalCode,
      ].joinNotEmpty(', '),
    ].joinNotEmpty(' - ');
  }
}
