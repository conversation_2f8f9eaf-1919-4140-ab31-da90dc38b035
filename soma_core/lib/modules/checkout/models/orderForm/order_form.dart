import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_core/modules/auth/models/user_orders/gift_card.dart';
import 'package:soma_core/modules/checkout/models/orderForm/merchant_seller_payments.dart';
import 'package:soma_core/modules/checkout/models/orderForm/payment.dart';
import 'package:soma_core/modules/checkout/models/orderForm/available_accounts.dart';
import 'package:soma_core/modules/checkout/models/orderForm/shipping_data_order_form/shipping_data_order_form.dart';
import 'totalizers.dart';
import 'package:soma_core/modules/checkout/models/index.dart';
import 'client_preferences_data.dart';
import 'client_profile_data.dart';
import 'payment_data.dart';
import 'rates_and_benefits_data.dart';
import 'store_preferences_data.dart';

class OrderForm {
  String? orderFormId;
  String? salesChannel;
  bool? loggedIn;
  bool? isCheckedIn;
  bool? isFeatureItem;
  dynamic storeId;
  dynamic checkedInPickupPointId;
  bool? allowManualPrice;
  bool? canEditData;
  dynamic userProfileId;
  dynamic userType;
  bool? ignoreProfileData;
  num? value;
  List<Messages>? messages;
  List<ProdutcOrderForm>? items;
  List<SelectableGifts>? selectableGifts;
  String? selectedGiftsId;
  List<Totalizers>? totalizers;
  ShippingDataOrderForm? shippingData;
  ClientProfileData? clientProfileData;
  PaymentData? paymentData;
  MarketingData? marketingData;
  List<dynamic>? sellers;
  ClientPreferencesData? clientPreferencesData;
  dynamic commercialConditionData;
  StorePreferencesData? storePreferencesData;
  dynamic giftRegistryData;
  OpenTextField? openTextField;
  dynamic invoiceData;
  dynamic customData;
  dynamic itemMetadata;
  dynamic hooksData;
  RatesAndBenefitsData? ratesAndBenefitsData;
  dynamic subscriptionData;
  dynamic itemsOrdination;
  final String? recaptchaKey;
  final String? recaptchaKeyV3;
  List<Map<String, dynamic>>? persistentCart;

  OrderForm({
    this.orderFormId,
    this.salesChannel,
    this.loggedIn,
    this.isCheckedIn,
    this.isFeatureItem,
    this.storeId,
    this.checkedInPickupPointId,
    this.allowManualPrice,
    this.canEditData,
    this.userProfileId,
    this.userType,
    this.ignoreProfileData,
    this.value,
    this.messages,
    this.items,
    this.selectableGifts,
    this.selectedGiftsId,
    this.totalizers,
    this.shippingData,
    this.clientProfileData,
    this.paymentData,
    this.marketingData,
    this.sellers,
    this.clientPreferencesData,
    this.commercialConditionData,
    this.storePreferencesData,
    this.giftRegistryData,
    this.openTextField,
    this.invoiceData,
    this.customData,
    this.itemMetadata,
    this.hooksData,
    this.ratesAndBenefitsData,
    this.subscriptionData,
    this.itemsOrdination,
    this.recaptchaKey,
    this.recaptchaKeyV3,
    this.persistentCart,
  });

  bool get hasProductToSendGift {
    var hasSendToGift = items
        ?.where((element) =>
            element.bundleItems != null && element.bundleItems!.isNotEmpty)
        .toList();

    if (hasSendToGift != null && hasSendToGift.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }

  PaymentOrderForm? get firstSelectedPayment {
    if (paymentData != null &&
        paymentData!.payments != null &&
        paymentData!.payments!.isNotEmpty) {
      return paymentData?.payments?.first;
    }
    return null;
  }

  List<MerchantSellerPayments> get merchantSellerPayments {
    return firstSelectedPayment?.merchantSellerPayments ?? [];
  }

  List<GiftCard> get getGiftCardsAdded {
    List<GiftCard> giftCards = [];

    if (paymentData != null && paymentData?.giftCards != null) {
      for (var item in paymentData!.giftCards!) {
        giftCards.add(item);
      }
    }

    return giftCards;
  }

  List<ProdutcOrderForm>? get getItems => items?.reversed.toList();

  List<ProdutcOrderForm> get getItemsSendToGift {
    List<ProdutcOrderForm> items = [];

    for (var item in getAddedItemsSendToGift) {
      if (item.offerings != null && item.offerings!.isNotEmpty) {
        items.add(item);
      }
    }
    return items;
  }

  List<ProdutcOrderForm> get getAddedItemsSendToGift {
    List<ProdutcOrderForm> addedItems = [];

    if (items != null) {
      for (var item in items!) {
        if (item.quantity != null) {
          for (int i = 0; i < item.quantity!.toInt(); i++) {
            if (item.sellingPrice != 0) addedItems.add(item);
          }
        }
      }
    }
    return addedItems;
  }

  List<ProdutcOrderForm> get getAddedItems {
    List<ProdutcOrderForm> addedItems = [];

    if (items != null) {
      for (var item in items!) {
        if (item.sellingPrice != 0) addedItems.add(item);
      }
    }
    return addedItems;
  }

  List<ProdutcOrderForm> get getAddedItemsGifts {
    List<ProdutcOrderForm> addedItemsGift = [];

    if (items != null) {
      for (var item in items!) {
        if (item.sellingPrice == 0) addedItemsGift.add(item);
      }
    }
    return addedItemsGift;
  }

  bool get isEmpty => getAddedItems.isEmpty ? true : false;

  num? get getItemsLength => items?.length;

  num? get getItemsQuantity =>
      (items != null && items!.isNotEmpty && items![0].getQuantity != null)
          ? items!
              .map((item) => item.quantity)
              .reduce((value, element) => value! + element!)
          : 0;

  ClientProfileData? get getClientProfileData => clientProfileData;

  String get getTotalItemsFormatted {
    var index = totalizers!.indexWhere((e) => e.id == 'Items');

    var indexShipping = totalizers!.indexWhere((e) => e.id == 'Shipping');

    return index != -1 && indexShipping != -1
        ? CurrencyUtils.format(
            amount: totalizers![index].value + totalizers![indexShipping].value,
            dividedBy100: true)
        : '';
  }

  String get getTotalItemsWithoutShippingFormatted {
    var index = totalizers!.indexWhere((e) => e.id == 'Items');
    return index != -1
        ? CurrencyUtils.format(
            amount: totalizers![index].value, dividedBy100: true)
        : '';
  }

  num get getTotalItemsValue {
    return totalizers?.firstWhereOrNull((e) => e.id == 'Items')?.value ?? 0;
  }

  List<AvailableAccounts> get getAvailableAccounts {
    var availableAccounts = paymentData!.availableAccounts!
        .map((s) => AvailableAccounts.fromMap(s))
        .toList();

    var cards = <AvailableAccounts>[];

    for (var element in availableAccounts) {
      var duplicated =
          cards.indexWhere((e) => e.cardNumber == element.cardNumber) > -1;

      if (!duplicated) {
        cards.add(element);
      }
    }

    return cards;
  }

  List<InstallmentsOptions> get getInstallmentOptions {
    var installmentOptions = paymentData!.installmentOptions!
        .map((s) => InstallmentsOptions.fromMap(s))
        .toList();

    return installmentOptions;
  }

  String get getCurrentInstallment {
    try {
      if (paymentData?.payments?.first.merchantSellerPayments != null &&
          paymentData!.payments!.first.merchantSellerPayments.length > 1) {
        return '${paymentData?.payments?.first.installments}x de ${CurrencyUtils.format(amount: ((paymentData?.payments?.first.value ?? 1) / (paymentData?.payments?.first.installments ?? 1)), dividedBy100: true)}';
      }
      var installments = paymentData
          ?.payments?.first.merchantSellerPayments.first.installments;
      var installmentValue = paymentData
          ?.payments?.first.merchantSellerPayments.first.installmentValue;

      return '${installments}x de ${CurrencyUtils.format(amount: installmentValue, dividedBy100: true)}';
    } catch (_) {
      return '1 x de R\$ ${CurrencyUtils.format(amount: value, dividedBy100: true)}';
    }
  }

  int get currentInstallmentCountSelected {
    return paymentData?.payments?.firstOrNull?.installments ?? 0;
  }

  String get lastInstallment {
    if (getInstallmentOptions.isNotEmpty &&
        getInstallmentOptions[0].installments != null &&
        getInstallmentOptions[0].installments!.isNotEmpty) {
      return 'até ${getInstallments().last.count}x de ${CurrencyUtils.format(dividedBy100: true, amount: getInstallments().last.value)} sem juros';
    } else if (getInstallmentOptions.isNotEmpty &&
        getInstallmentOptions[0].installments!.isEmpty) {
      return '1 x de ${CurrencyUtils.format(dividedBy100: true, amount: getInstallmentOptions[0].value)} sem juros';
    }

    return '';
  }

  String get maxInstallments {
    if (getInstallmentOptions.isNotEmpty &&
        getInstallmentOptions[0].installments != null &&
        getInstallmentOptions[0].installments!.isNotEmpty) {
      return 'em até ${getInstallments().last.count}x';
    }
    return 'em 1x';
  }

  List<InstallmentOrderForm> getInstallments() {
    List<InstallmentOrderForm> installments = [];

    for (var element in getInstallmentOptions[0].installments!) {
      if (element.value != null) {
        installments.add(
          element,
        );
      }
    }
    return installments;
  }

  List<ProdutcOrderForm> get getUnavailableItems {
    return getItems!
        .where((product) => product.availability != 'available')
        .toList();
  }

  bool get acceptsPixPayment {
    const kPixPaymentSystemName = 'Pagaleve Pix A Vista Transparente';
    const kPixPaymentSystemGroupName =
        'PagalevePixAVistaTransparentePaymentGroup';
    return paymentData?.paymentSystems?.any((ps) =>
            ps.name == kPixPaymentSystemName &&
            ps.groupName == kPixPaymentSystemGroupName) ??
        false;
  }

  bool get acceptsPixInstallments {
    const kPixInstallmentsPaymentSystemName = 'Pagaleve Transparente';
    const kPixInstallmentsPaymentSystemGroupName =
        'Pagaleve TransparentePaymentGroup';

    return paymentData?.paymentSystems?.any((ps) =>
            ps.name == kPixInstallmentsPaymentSystemName &&
            ps.groupName == kPixInstallmentsPaymentSystemGroupName) ??
        false;
  }

  bool get acceptsGiftcardPayment {
    const kGiftCardPaymentSystemGroupName = 'giftCardPaymentGroup';
    return paymentData?.paymentSystems
            ?.any((ps) => ps.groupName == kGiftCardPaymentSystemGroupName) ??
        false;
  }

  bool get acceptsApplePayment {
    const kApplePaymentSystemGroupName = 'ApplePayPaymentGroup';
    return paymentData?.paymentSystems
            ?.any((ps) => ps.groupName == kApplePaymentSystemGroupName) ??
        true;
  }

  bool get acceptsCreditCardPayment {
    const kCreditCardPaymentSystemGroupName = 'creditCardPaymentGroup';
    return paymentData?.paymentSystems
            ?.any((ps) => ps.groupName == kCreditCardPaymentSystemGroupName) ??
        false;
  }

  bool acceptsPaymentMethod(PaymentMethodOptions paymentMethodOption) {
    switch (paymentMethodOption) {
      case PaymentMethodOptions.pixInstallments:
        return acceptsPixInstallments;
      case PaymentMethodOptions.pix:
        return acceptsPixPayment;
      case PaymentMethodOptions.creditCard:
        return acceptsCreditCardPayment;
      case PaymentMethodOptions.cashback:
      case PaymentMethodOptions.giftCard:
        return acceptsGiftcardPayment;
      case PaymentMethodOptions.applePay:
        return acceptsApplePayment;
    }
  }

  bool get hasProductSendToGigt {
    var fisrtProductSendToGift = getItems?.firstWhereOrNull(
        (e) => e.bundleItems != null && e.bundleItems!.isNotEmpty);

    if (fisrtProductSendToGift != null) {
      return true;
    } else {
      return false;
    }
  }

  bool isCouponApplied(String? coupon) {
    return marketingData?.coupon?.toLowerCase() == coupon?.toLowerCase() ||
        (ratesAndBenefitsData?.rateAndBenefitsIdentifiers
                ?.whereType<Map<String, dynamic>>()
                .map((r) => r['matchedParameters'])
                .whereType<Map<String, dynamic>>()
                .any((p) =>
                    p['couponCode@Marketing']?.toLowerCase() ==
                    coupon?.toLowerCase()) ??
            false);
  }

  String? getAppliedCoupon() {
    if (marketingData?.coupon != null) {
      return marketingData!.coupon;
    }

    if (ratesAndBenefitsData?.rateAndBenefitsIdentifiers != null) {
      for (var identifier
          in ratesAndBenefitsData!.rateAndBenefitsIdentifiers!) {
        if (identifier is Map<String, dynamic>) {
          var matchedParameters = identifier['matchedParameters'];
          if (matchedParameters is Map<String, dynamic>) {
            var couponCode = matchedParameters['couponCode@Marketing'];
            if (couponCode != null) {
              return couponCode;
            }
          }
        }
      }
    }

    return null;
  }

  @override
  String toString() {
    return 'OrderForm(orderFormId: $orderFormId, salesChannel: $salesChannel, loggedIn: $loggedIn, isCheckedIn: $isCheckedIn, storeId: $storeId, checkedInPickupPointId: $checkedInPickupPointId, allowManualPrice: $allowManualPrice, canEditData: $canEditData, userProfileId: $userProfileId, userType: $userType, ignoreProfileData: $ignoreProfileData, value: $value, messages: $messages, items: $items, selectableGifts: $selectableGifts, totalizers: $totalizers, shippingData: $shippingData, clientProfileData: $clientProfileData, paymentData: $paymentData, marketingData: $marketingData, sellers: $sellers, clientPreferencesData: $clientPreferencesData, commercialConditionData: $commercialConditionData, storePreferencesData: $storePreferencesData, giftRegistryData: $giftRegistryData, openTextField: $openTextField, invoiceData: $invoiceData, customData: $customData, itemMetadata: $itemMetadata, hooksData: $hooksData, ratesAndBenefitsData: $ratesAndBenefitsData, subscriptionData: $subscriptionData, itemsOrdination: $itemsOrdination)';
  }

  static List<SelectableGifts> parseSelectableGifts(Map<String, dynamic> json) {
    List<SelectableGifts> selectableGiftsList = [];

    try {
      var availableGifts = json['selectableGifts'][0]['availableGifts'];
      for (final gifts in availableGifts) {
        selectableGiftsList.add(SelectableGifts.fromJson(gifts));
      }
      return selectableGiftsList;
    } catch (_) {
      return selectableGiftsList;
    }
  }

  static String parseSelectableGiftsId(Map<String, dynamic> json) {
    String id = "";
    try {
      id = json['selectableGifts'][0]['id'];
      return id;
    } catch (_) {
      return id;
    }
  }

  bool get hasGiftSelected {
    return selectableGifts?.any((element) => element.isSelected == true) ??
        false;
  }

  List<String> get selectableGiftsProductIds {
    List<String> ids = [];
    selectableGifts?.forEach((e) => ids.add(e.id));
    return ids;
  }

  List<String> get selectableGiftsSkuIds {
    List<String> ids = [];
    selectableGifts?.forEach((e) => ids.add(e.id));
    return ids;
  }

  factory OrderForm.fromJson(Map<String, dynamic> json) => OrderForm(
        orderFormId: json['orderFormId'] as String?,
        salesChannel: json['salesChannel'] as String?,
        loggedIn: json['loggedIn'] as bool?,
        isCheckedIn: json['isCheckedIn'] as bool?,
        storeId: json['storeId'],
        checkedInPickupPointId: json['checkedInPickupPointId'],
        allowManualPrice: json['allowManualPrice'] as bool?,
        canEditData: json['canEditData'] as bool?,
        userProfileId: json['userProfileId'],
        userType: json['userType'],
        ignoreProfileData: json['ignoreProfileData'] as bool?,
        value: json['value'] as num?,
        messages: (json['messages'] as List<dynamic>?)?.map((e) {
          if (e.runtimeType == String) {
            return Messages.fromJson(e as String);
          } else {
            return Messages.fromMap(e as Map<String, dynamic>);
          }
        }).toList(),
        items: (json['items'] as List<dynamic>?)
            ?.map((e) => ProdutcOrderForm.fromJson(e as Map<String, dynamic>))
            .toList(),
        selectableGifts: parseSelectableGifts(json),
        selectedGiftsId: parseSelectableGiftsId(json),
        totalizers: (json['totalizers'] as List<dynamic>?)
            ?.map((e) => Totalizers.fromJson(e as Map<String, dynamic>))
            .toList(),
        shippingData: json['shippingData'] == null
            ? null
            : ShippingDataOrderForm.fromJson(
                json['shippingData'] as Map<String, dynamic>),
        clientProfileData: json['clientProfileData'] == null
            ? null
            : ClientProfileData.fromJson(
                json['clientProfileData'] as Map<String, dynamic>),
        paymentData: json['paymentData'] == null
            ? null
            : PaymentData.fromJson(json['paymentData'] as Map<String, dynamic>),
        marketingData: json['marketingData'] == null
            ? null
            : MarketingData.fromMap(
                json['marketingData'] as Map<String, dynamic>),
        sellers: json['sellers'] as List<dynamic>?,
        clientPreferencesData: json['clientPreferencesData'] == null
            ? null
            : ClientPreferencesData.fromJson(
                json['clientPreferencesData'] as Map<String, dynamic>),
        commercialConditionData: json['commercialConditionData'],
        storePreferencesData: json['storePreferencesData'] == null
            ? null
            : StorePreferencesData.fromJson(
                json['storePreferencesData'] as Map<String, dynamic>),
        giftRegistryData: json['giftRegistryData'],
        openTextField: json['openTextField'] == null
            ? null
            : OpenTextField.fromJson(
                json['openTextField'] as Map<String, dynamic>),
        invoiceData: json['invoiceData'],
        customData: json['customData'],
        itemMetadata: json['itemMetadata'],
        hooksData: json['hooksData'],
        ratesAndBenefitsData: json['ratesAndBenefitsData'] == null
            ? null
            : RatesAndBenefitsData.fromJson(
                json['ratesAndBenefitsData'] as Map<String, dynamic>),
        subscriptionData: json['subscriptionData'],
        itemsOrdination: json['itemsOrdination'],
        recaptchaKeyV3: json['recaptchaKeyV3'],
        recaptchaKey: json['recaptchaKey'],
        persistentCart:
            json['persistentCart'] == null ? null : json['persistentCart'],
      );

  Map<String, dynamic> toJson() => {
        'orderFormId': orderFormId,
        'salesChannel': salesChannel,
        'loggedIn': loggedIn,
        'isCheckedIn': isCheckedIn,
        'storeId': storeId,
        'checkedInPickupPointId': checkedInPickupPointId,
        'allowManualPrice': allowManualPrice,
        'canEditData': canEditData,
        'userProfileId': userProfileId,
        'userType': userType,
        'ignoreProfileData': ignoreProfileData,
        'value': value,
        'messages': messages?.map((m) => m.toJson()).toList(),
        'items': items?.map((m) => m.toJson()).toList(),
        'selectableGifts': selectableGifts,
        'totalizers': totalizers?.map((t) => t.toJson()).toList(),
        'shippingData': shippingData?.toJson(),
        'clientProfileData': clientProfileData?.toJson(),
        'paymentData': paymentData?.toJson(),
        'marketingData': marketingData?.toJson(),
        'sellers': sellers,
        'clientPreferencesData': clientPreferencesData?.toJson(),
        'commercialConditionData': commercialConditionData,
        'storePreferencesData': storePreferencesData?.toJson(),
        'giftRegistryData': giftRegistryData,
        'openTextField': openTextField?.toJson(),
        'invoiceData': invoiceData,
        'customData': customData,
        'itemMetadata': itemMetadata,
        'hooksData': hooksData,
        'ratesAndBenefitsData': ratesAndBenefitsData?.toJson(),
        'subscriptionData': subscriptionData,
        'itemsOrdination': itemsOrdination,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is OrderForm &&
        listEquals(other.messages, messages) &&
        listEquals(other.items, items) &&
        listEquals(other.selectableGifts, selectableGifts) &&
        listEquals(other.totalizers, totalizers) &&
        listEquals(other.sellers, sellers) &&
        other.orderFormId == orderFormId &&
        other.salesChannel == salesChannel &&
        other.loggedIn == loggedIn &&
        other.isCheckedIn == isCheckedIn &&
        other.storeId == storeId &&
        other.checkedInPickupPointId == checkedInPickupPointId &&
        other.allowManualPrice == allowManualPrice &&
        other.canEditData == canEditData &&
        other.userProfileId == userProfileId &&
        other.userType == userType &&
        other.ignoreProfileData == ignoreProfileData &&
        other.value == value &&
        other.shippingData == shippingData &&
        other.clientProfileData == clientProfileData &&
        other.paymentData == paymentData &&
        other.marketingData == marketingData &&
        other.clientPreferencesData == clientPreferencesData &&
        other.commercialConditionData == commercialConditionData &&
        other.storePreferencesData == storePreferencesData &&
        other.giftRegistryData == giftRegistryData &&
        other.openTextField == openTextField &&
        other.invoiceData == invoiceData &&
        other.customData == customData &&
        other.itemMetadata == itemMetadata &&
        other.hooksData == hooksData &&
        other.ratesAndBenefitsData == ratesAndBenefitsData &&
        other.subscriptionData == subscriptionData &&
        other.itemsOrdination == itemsOrdination;
  }

  @override
  int get hashCode =>
      orderFormId.hashCode ^
      salesChannel.hashCode ^
      loggedIn.hashCode ^
      isCheckedIn.hashCode ^
      storeId.hashCode ^
      checkedInPickupPointId.hashCode ^
      allowManualPrice.hashCode ^
      canEditData.hashCode ^
      userProfileId.hashCode ^
      userType.hashCode ^
      ignoreProfileData.hashCode ^
      value.hashCode ^
      messages.hashCode ^
      items.hashCode ^
      selectableGifts.hashCode ^
      totalizers.hashCode ^
      shippingData.hashCode ^
      clientProfileData.hashCode ^
      paymentData.hashCode ^
      marketingData.hashCode ^
      sellers.hashCode ^
      clientPreferencesData.hashCode ^
      commercialConditionData.hashCode ^
      storePreferencesData.hashCode ^
      giftRegistryData.hashCode ^
      openTextField.hashCode ^
      invoiceData.hashCode ^
      customData.hashCode ^
      itemMetadata.hashCode ^
      hooksData.hashCode ^
      ratesAndBenefitsData.hashCode ^
      subscriptionData.hashCode ^
      itemsOrdination.hashCode;
}
