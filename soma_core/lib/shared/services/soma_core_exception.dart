class SomaCoreException implements Exception {
  final String? message;
  final Object? cause;

  const SomaCoreException({this.message, this.cause});

  @override
  String toString() {
    return "$message";
  }
}

class SomaCoreExceptionCreditCardInvalid extends SomaCoreException {
  SomaCoreExceptionCreditCardInvalid({Object? cause})
      : super(message: 'credit card invalid', cause: cause);
}

class SomaCoreExceptionPaymentPixInvalid extends SomaCoreException {
  SomaCoreExceptionPaymentPixInvalid({Object? cause}) : super(cause: cause);
}

class SomaCoreExceptionInvalidPostCode extends SomaCoreException {
  SomaCoreExceptionInvalidPostCode({required super.message, Object? cause})
      : super(cause: cause);
}

class SomaCoreExceptionGiftCardInvalid extends SomaCoreException {
  SomaCoreExceptionGiftCardInvalid({Object? cause})
      : super(message: 'gift card card invalid', cause: cause);
}

class SomaCoreExceptionPinCodeInvalid extends SomaCoreException {
  SomaCoreExceptionPinCodeInvalid({Object? cause})
      : super(message: 'pin code invalid', cause: cause);
}

class SomaCoreExceptionWrongCredentials extends SomaCoreException {
  SomaCoreExceptionWrongCredentials({required super.message, super.cause});
}

class SomaCoreExceptionInvalidFolderNameWishlist extends SomaCoreException {
  SomaCoreExceptionInvalidFolderNameWishlist({Object? cause})
      : super(message: 'error SyncWishlist', cause: cause);
}

class SomaCoreExceptionSyncWishlist extends SomaCoreException {
  SomaCoreExceptionSyncWishlist({Object? cause})
      : super(message: 'error SyncWishlist', cause: cause);
}

class SomaCoreExceptionFolderNotFoundWishlist extends SomaCoreException {
  SomaCoreExceptionFolderNotFoundWishlist({Object? cause})
      : super(message: 'folder not found', cause: cause);
}

class SomaCoreExceptionInvalidCoupon extends SomaCoreException {
  SomaCoreExceptionInvalidCoupon({Object? cause})
      : super(message: 'O cupom informado é inválido', cause: cause);
}

class SomaCoreExceptionNotAddeddCoupon extends SomaCoreException {
  SomaCoreExceptionNotAddeddCoupon({Object? cause})
      : super(message: 'Cupom não aplicado. Confira as regras.', cause: cause);
}

class SomaCoreUserDocumentNotFound extends SomaCoreException {
  SomaCoreUserDocumentNotFound({Object? cause})
      : super(
          message: 'Cadastre seu CPF em Dados Pessoais',
          cause: cause,
        );
}

class SomaCoreUserPaymentFailed extends SomaCoreException {
  SomaCoreUserPaymentFailed() : super(message: 'pagamento não aprovado');
}

class SomaCoreUserCardErrorNotAuthorizedFailed extends SomaCoreException {
  SomaCoreUserCardErrorNotAuthorizedFailed()
      : super(
          cause: 'Pagamento não aprovado',
          message:
              'Verifique seus dados do cartão, limite ou se ele está bloqueado e tente novamente',
        );
}

class SomaCoreUserCardCanceledFailed extends SomaCoreException {
  SomaCoreUserCardCanceledFailed()
      : super(
          cause: 'Um ou mais meios de pagamentos não foram autorizados',
          message: 'Por favor, tente novamente',
        );
}

class SomaCoreUserCardGenericErrorFailed extends SomaCoreException {
  SomaCoreUserCardGenericErrorFailed()
      : super(
          cause: 'Erro inesperado no processamento',
          message:
              'Por favor, tente novamente ou se precisar de mais alguma ajuda, entre em contato com a sua vendedora',
        );
}

class SomaCoreUserCardInsufficientAmountFailed extends SomaCoreException {
  SomaCoreUserCardInsufficientAmountFailed()
      : super(
          cause: 'Pagamento não aprovado',
          message:
              'A soma dos meios de pagamento não corresponde ao valor total. Revise e tente novamente.',
        );
}

class SomaCoreUserCardExcessCardsFailed extends SomaCoreException {
  SomaCoreUserCardExcessCardsFailed()
      : super(
          cause: 'Pagamento não aprovado',
          message:
              'Foi detectada a tentativa de usar mais de dois meios de pagamento. Por favor, revise e tente novamente.',
        );
}

class SomaCoreUserCardInvalidPaymentMethodFailed extends SomaCoreException {
  SomaCoreUserCardInvalidPaymentMethodFailed()
      : super(
          cause: 'Pagamento não aprovado',
          message:
              'Este meio de pagamento não é permitido. Por favor, escolha outro e tente novamente.',
        );
}

class UseOfDisabledFeatureException extends SomaCoreException {
  const UseOfDisabledFeatureException(String configName)
      : super(
          message: 'Ocorreu uma tentativa de utilização de funcionalidades '
              'relacionadas à $configName porém a configuração está desabilitada. '
              'Verifique se seu componente está respeitando as configurações da marca',
        );

  const UseOfDisabledFeatureException.storePickup() : this('StorePickupConfig');
}

class OrderReturnsRequestExecption extends SomaCoreException {
  OrderReturnsRequestExecption({Object? cause})
      : super(
            message: 'Erro ao executar a requisição de devolução',
            cause: cause);
}
