import 'package:flutter/material.dart';

class AddToBagBottomSheetTheme {
  final AddToBagSizeSelector sizeSelector;
  final FullLookAddToBagTheme? fullLookAddToBagTheme;
  final SizeButtonsTheme? sizeButtonsTheme;
  final SizePickerTheme? sizePickerTheme;
  final ProductCardBottomSheetTheme? productCardBottomSheetTheme;

  const AddToBagBottomSheetTheme({
    this.sizeSelector = AddToBagSizeSelector.sizePicker,
    this.fullLookAddToBagTheme,
    this.sizeButtonsTheme,
    this.sizePickerTheme,
    this.productCardBottomSheetTheme,
  });
}

class FullLookAddToBagTheme {
  final AddToBagHeaderInfo headerInfo;
  final bool? hideHeader;
  final bool? hideButton;
  final double? spacingBetweenItemInfoAndProducts;
  final double? bottomSheetHeight;
  final FullLookSpotProductTheme? spotProductTheme;
  final TextStyle? subtitleStyle;
  final double? closeButtonRightPadding;
  final double? emptyFullLookBottomSheetHeight;
  final double? bottomSheetHeightEmpty;

  const FullLookAddToBagTheme({
    this.headerInfo = AddToBagHeaderInfo.selectedItemSize,
    this.hideHeader,
    this.hideButton,
    this.spacingBetweenItemInfoAndProducts,
    this.bottomSheetHeight,
    this.spotProductTheme,
    this.subtitleStyle,
    this.closeButtonRightPadding,
    this.emptyFullLookBottomSheetHeight,
    this.bottomSheetHeightEmpty,
  });
}

class SizeButtonsTheme {
  final bool redirectToProductCardWhenInWishlist;

  const SizeButtonsTheme({
    required this.redirectToProductCardWhenInWishlist,
  });
}

class SizePickerTheme {
  final bool hasTableSizeAndMySizeIsOverButtonsInAddToBagBottomSheet;

  const SizePickerTheme({
    required this.hasTableSizeAndMySizeIsOverButtonsInAddToBagBottomSheet,
  });
}

class FullLookSpotProductTheme {
  final double? imageWidth;
  final double? imageHeight;
  final bool? enableBackgroundFavoriteIcon;
  final double? textLeftSpacing;
  final double? textRightSpacing;
  final double? textTopSpacing;
  final TextStyle? currentPriceTextStyle;
  final TextStyle? oldPriceTextStyle;
  final Color? onSaleTextColor;

  const FullLookSpotProductTheme({
    this.imageWidth,
    this.imageHeight,
    this.enableBackgroundFavoriteIcon,
    this.textLeftSpacing,
    this.textRightSpacing,
    this.textTopSpacing,
    this.currentPriceTextStyle,
    this.oldPriceTextStyle,
    this.onSaleTextColor,
  });
}

class ProductCardBottomSheetTheme {
  final double? containerHeight;
  final bool showOtherLookProducts;
  final bool showPixDiscountTag;

  const ProductCardBottomSheetTheme({
    this.containerHeight,
    this.showOtherLookProducts = false,
    this.showPixDiscountTag = false,
  });
}

enum AddToBagSizeSelector {
  sizePicker,
  sizeButtons;

  bool get isSizePicker => this == AddToBagSizeSelector.sizePicker;
  bool get isSizeButtons => this == AddToBagSizeSelector.sizeButtons;
}

enum AddToBagHeaderInfo { selectedItemFullInfo, selectedItemSize }
