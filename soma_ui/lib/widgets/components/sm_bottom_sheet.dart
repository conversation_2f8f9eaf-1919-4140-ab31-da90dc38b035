import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';

class SMBottomSheet extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final ImageProvider? image;
  final bool? buttonDisabled;
  final bool buttonIsLoading;
  final double? iconSize;
  final double? buttomTitleSize;
  final double? spinnerInactiveColorOpacity;
  final void Function()? onTapCallToAction;
  final void Function()? onTap;
  final void Function()? onTapClose;
  final String? callToAction;
  final String? subtitle;
  final String? title;
  final String? buttonTitle;
  final Color? buttonBackgroundColor;
  final Color? backgroundColor;
  final Color? buttonTextColor;
  final ButtonHeight? buttonHeight;
  final String? secondButtonTitle;
  final bool secondButtonDisabled;
  final bool secondButtonIsLoading;
  final Color? secondButtonBackgroundColor;
  final Color? secondButtonTextColor;
  final void Function()? onTapSecondButton;
  final bool? hideButton;
  final bool? hideHeader;
  final int buttonFlex;
  final int secondButtonFlex;
  final Color? spinnerActiveColor;
  final Color? spinnerInactiveColor;
  final TextStyle? callToActionStyle;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final double? paddingBottomTitle;
  final WidgetBuilder? bottom;
  final double? childHeightFactor;
  final EdgeInsets? padding;
  final bool secondButtonIsColumn;
  final List<String>? multiplesSubtitle;
  final bool hideCloseButton;
  final Widget? buttonLeadingIcon;

  final double? paddingBottomSubtitle;
  final double? closeButtonRightPadding;
  final bool hasMinWidth;
  final SMButtonStyle? primaryButtonStyle;
  final SMButtonStyle? secondaryButtonStyle;
  final bool showDragHandling;
  final Widget? secondButtonTrailing;
  final EdgeInsets? headerPadding;
  final bool autoHeight;
  final bool useTextTransform;
  final String? fixedTitle;
  final TextStyle? fixedTitleStyle;
  final ImageProvider? fixedHeaderImage;
  final double? fixedHeaderImageHeight;
  final EdgeInsets? headerTitlePadding;

  const SMBottomSheet({
    super.key,
    this.buttonIsLoading = false,
    this.secondButtonDisabled = false,
    this.secondButtonIsLoading = false,
    this.backgroundColor,
    this.callToAction,
    this.onTapCallToAction,
    this.callToActionStyle,
    this.titleStyle,
    this.subtitleStyle,
    this.subtitle,
    this.buttonBackgroundColor,
    this.buttonDisabled = false,
    this.isLoading = false,
    this.iconSize = 12,
    this.buttomTitleSize,
    this.spinnerInactiveColorOpacity = 0.5,
    this.spinnerActiveColor,
    this.spinnerInactiveColor,
    this.buttonTextColor,
    this.secondButtonTitle,
    this.secondButtonBackgroundColor,
    this.secondButtonTextColor,
    this.onTapSecondButton,
    this.buttonFlex = 4,
    this.secondButtonFlex = 3,
    this.image,
    this.onTap,
    this.buttonTitle,
    this.buttonHeight,
    this.hideButton = false,
    this.hideHeader = false,
    this.title,
    this.bottom,
    this.childHeightFactor,
    this.padding,
    this.onTapClose,
    required this.child,
    this.secondButtonIsColumn = false,
    this.multiplesSubtitle,
    this.hideCloseButton = false,
    this.paddingBottomTitle,
    this.buttonLeadingIcon,
    this.paddingBottomSubtitle,
    this.closeButtonRightPadding,
    this.primaryButtonStyle,
    this.secondaryButtonStyle,
    this.hasMinWidth = false,
    this.showDragHandling = false,
    this.secondButtonTrailing,
    this.headerPadding,
    this.autoHeight = false,
    this.useTextTransform = true,
    this.fixedTitle,
    this.fixedTitleStyle,
    this.fixedHeaderImage,
    this.fixedHeaderImageHeight,
    this.headerTitlePadding,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final footer = _buildFooter(tokens);

    final callToActionStyle =
        this.callToActionStyle ?? tokens.typography.typeStyles.bodySm;
    final backgroundColor = this.backgroundColor ?? tokens.colors.neutral.pure1;
    final titleStyle =
        this.titleStyle ?? tokens.typography.typeStyles.headlineSm;
    final subtitleStyle =
        this.subtitleStyle ?? tokens.typography.typeStyles.subtitle;
    final fixedTitleStyle =
        this.fixedTitleStyle ?? tokens.typography.typeStyles.headlineSm;

    return DecoratedBox(
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: backgroundColor,
          width: 0.1,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(tokens.borderRadius.radiusLarge),
          topRight: Radius.circular(tokens.borderRadius.radiusLarge),
        ),
      ),
      child: SafeArea(
        top: false,
        left: false,
        right: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: padding ?? EdgeInsets.all(tokens.spacingInset.lg),
              child: Column(children: [
                if (showDragHandling)
                  Align(
                    alignment: Alignment.center,
                    child: Container(
                      height: 4,
                      width: 72,
                      decoration: BoxDecoration(
                        color: tokens.colors.neutral.dark1,
                        borderRadius: BorderRadius.circular(2.5),
                      ),
                    ),
                  ),
                if (fixedTitle != null && fixedTitle!.isNotEmpty) ...[
                  if (showDragHandling || fixedHeaderImage != null)
                    SizedBox(height: tokens.spacingStack.md),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: headerTitlePadding ?? EdgeInsets.zero,
                      child: Text(
                        fixedTitle!,
                        style: fixedTitleStyle,
                      ),
                    ),
                  ),
                ],
              ]),
            ),
            if (autoHeight)
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: padding ??
                        EdgeInsets.only(
                            bottom: tokens.spacingInset.lg,
                            top: 0,
                            left: tokens.spacingInset.lg,
                            right: tokens.spacingInset.lg),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: headerPadding ??
                                  EdgeInsets.only(
                                      right: closeButtonRightPadding ?? 0,
                                      bottom: title != null
                                          ? tokens.bottomSheetSizeTableTheme
                                                  .hasEmptyBottomSheetSizeTable
                                              ? 0
                                              : tokens.spacingStack.lg
                                          : 0),
                              child: !hideCloseButton
                                  ? IconButton(
                                      onPressed: onTapClose ??
                                          Navigator.of(context).pop,
                                      icon:
                                          Icon(tokens.icons.close, size: 36.0),
                                    )
                                  : null,
                            ),
                          ],
                        ),
                        if (hideHeader == false)
                          SMMainContent(
                            image: image,
                            title: title,
                            subtitle: subtitle,
                            titleStyle: titleStyle,
                            subtitleStyle: subtitleStyle,
                            multiplesSubtitle: multiplesSubtitle,
                            titlePadding: EdgeInsets.only(
                              bottom:
                                  paddingBottomTitle ?? tokens.spacingStack.md,
                            ),
                          ),
                        if (callToAction != null && callToAction!.isNotEmpty)
                          Padding(
                            padding:
                                EdgeInsets.only(top: tokens.spacingStack.lg),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                GestureDetector(
                                  onTap: onTapCallToAction,
                                  child: Text(
                                    tokens.textTransform.body(callToAction!),
                                    style: callToActionStyle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        child,
                      ],
                    ),
                  ),
                  if (hideButton == false)
                    Padding(
                      padding: EdgeInsets.only(
                        left: tokens.spacingInset.lg,
                        right: tokens.spacingInset.lg,
                        bottom: tokens.spacingInset.lg,
                      ),
                      child: _buildFooter(tokens),
                    ),
                ],
              )
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: padding ??
                            EdgeInsets.only(
                                bottom: tokens.spacingInset.lg,
                                top: 0,
                                left: tokens.spacingInset.lg,
                                right: tokens.spacingInset.lg),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Padding(
                                  padding: headerPadding ??
                                      EdgeInsets.only(
                                          right: closeButtonRightPadding ?? 0,
                                          bottom: title != null
                                              ? tokens.bottomSheetSizeTableTheme
                                                      .hasEmptyBottomSheetSizeTable
                                                  ? 0
                                                  : tokens.spacingStack.lg
                                              : 0),
                                  child: !hideCloseButton
                                      ? IconButton(
                                          onPressed: onTapClose ??
                                              Navigator.of(context).pop,
                                          icon: Icon(tokens.icons.close,
                                              size: 36.0),
                                        )
                                      : null,
                                ),
                              ],
                            ),
                            if (hideHeader == false)
                              SMMainContent(
                                image: image,
                                title: title,
                                subtitle: subtitle,
                                titleStyle: titleStyle,
                                subtitleStyle: subtitleStyle,
                                multiplesSubtitle: multiplesSubtitle,
                                titlePadding: EdgeInsets.only(
                                  bottom: paddingBottomTitle ??
                                      tokens.spacingStack.md,
                                ),
                              ),
                            if (callToAction != null &&
                                callToAction!.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(
                                    top: tokens.spacingStack.lg),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    GestureDetector(
                                      onTap: onTapCallToAction,
                                      child: Text(
                                          tokens.textTransform
                                              .body(callToAction!),
                                          style: callToActionStyle),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                        left: tokens.spacingInline.xxs,
                                      ),
                                      child: Icon(
                                        tokens.icons.arrowRight,
                                        size: iconSize,
                                        color: tokens.colors.typography.pure2,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            SizedBox(height: tokens.spacingStack.xs),
                            ConstrainedBox(
                              constraints: BoxConstraints(
                                minWidth: hasMinWidth
                                    ? MediaQuery.of(context).size.width
                                    : 0,
                                maxHeight: MediaQuery.of(context).size.height *
                                    (childHeightFactor ?? 0.5),
                              ),
                              child: child,
                            ),
                            if (footer != null) ...[
                              SizedBox(
                                height: paddingBottomSubtitle ??
                                    tokens.spacingStack.md,
                              ),
                              footer,
                            ]
                          ],
                        ),
                      ),
                      if (bottom != null) bottom!(context),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFooter(DesignTokens tokens) {
    final buttonBackgroundColor =
        this.buttonBackgroundColor ?? tokens.colors.brand.pure1;
    final buttonTextColor =
        this.buttonTextColor ?? tokens.colors.typography.pure1;
    final secondButtonBackgroundColor =
        this.secondButtonBackgroundColor ?? Colors.transparent;
    final secondButtonTextColor =
        this.secondButtonTextColor ?? tokens.colors.typography.pure2;
    final buttomTitleSize =
        this.buttomTitleSize ?? tokens.typography.fontSizes.xxs;
    final spinnerActiveColor =
        this.spinnerActiveColor ?? tokens.colors.typography.pure1;
    final spinnerInactiveColor =
        this.spinnerInactiveColor ?? tokens.colors.typography.pure1;

    if (isLoading) {
      return Stack(
        alignment: Alignment.center,
        children: [
          Center(
            child: ClipOval(
              child: Container(
                height: 56.0,
                width: 56.0,
                color: buttonBackgroundColor,
              ),
            ),
          ),
          SMSpinner(
            activeColor: spinnerActiveColor,
            inactiveColor: spinnerInactiveColor,
          )
        ],
      );
    }

    if (secondButtonIsColumn &&
        secondButtonTitle != null &&
        secondButtonTitle!.isNotEmpty) {
      return Column(
        children: [
          SMButton.secondary(
            onPressed: onTapSecondButton,
            expanded: true,
            child: Text(
              tokens.textTransform.button(secondButtonTitle!),
              style: tokens.typography.typeStyles.subtitle.copyWith(
                fontSize: secondaryButtonStyle?.textStyle?.fontSize ??
                    buttomTitleSize,
                color: secondaryButtonStyle?.textStyle?.color ??
                    secondButtonTextColor,
              ),
            ),
            isDisabled: secondButtonDisabled,
            isLoading: secondButtonIsLoading,
            size: ButtonSize.large,
            style: secondaryButtonStyle,
            trailing: secondButtonTrailing,
          ),
          SizedBox(height: tokens.spacingInline.xs),
          SMButton.primary(
            onPressed: onTap,
            expanded: true,
            child: Text(
              buttonTitle != null && buttonTitle!.isNotEmpty
                  ? tokens.textTransform.button(buttonTitle!)
                  : '',
              style: tokens.typography.typeStyles.subtitle.copyWith(
                  fontSize: primaryButtonStyle?.textStyle?.fontSize ??
                      buttomTitleSize,
                  color: buttonDisabled!
                      ? tokens.buttonTheme.primaryButtonDefaultStyle
                              ?.disabledForegroundColor ??
                          primaryButtonStyle?.textStyle?.color ??
                          buttonTextColor
                      : primaryButtonStyle?.textStyle?.color ??
                          buttonTextColor),
            ),
            isLoading: buttonIsLoading,
            isDisabled: buttonDisabled!,
            size: ButtonSize.fromHeight(buttonHeight) ?? ButtonSize.large,
            style: primaryButtonStyle,
          )
        ],
      );
    }

    if (secondButtonTitle?.isNotEmpty ?? false) {
      return Row(
        children: [
          Flexible(
            flex: secondButtonFlex,
            fit: FlexFit.tight,
            child: SMButton.secondary(
              child: Text(tokens.textTransform.button(secondButtonTitle!)),
              onPressed: onTapSecondButton,
              isDisabled: secondButtonDisabled,
              isLoading: secondButtonIsLoading,
              style: SMButtonStyle(
                padding: EdgeInsets.all(tokens.spacingStack.sm),
                backgroundColor: secondButtonBackgroundColor,
                foregroundColor: secondButtonTextColor,
                textStyle: tokens.typography.typeStyles.subtitle
                    .copyWith(fontSize: buttomTitleSize),
                borderColor: tokens.colors.neutral.medium1,
              ),
              trailing: secondButtonTrailing,
            ),
          ),
          SizedBox(
            width: tokens.spacingInline.xs,
          ),
          Flexible(
            flex: buttonFlex,
            fit: FlexFit.tight,
            child: SMButton.primary(
              child: buttonTitle != null && buttonTitle!.isNotEmpty
                  ? Text(tokens.textTransform.button(buttonTitle!))
                  : const Text(''),
              onPressed: onTap,
              size: ButtonSize.fromHeight(buttonHeight) ?? ButtonSize.large,
              isDisabled: buttonDisabled ?? false,
              isLoading: buttonIsLoading,
              style: SMButtonStyle(
                padding: EdgeInsets.all(tokens.spacingStack.sm),
                backgroundColor: buttonBackgroundColor,
                foregroundColor: buttonTextColor,
                textStyle: tokens.typography.typeStyles.subtitle
                    .copyWith(fontSize: buttomTitleSize),
                borderColor: tokens.colors.neutral.medium1,
              ),
            ),
          ),
        ],
      );
    }
    if (hideButton == false) {
      return SMButton.primary(
        child: buttonTitle != null && buttonTitle!.isNotEmpty
            ? Text(tokens.textTransform.button(buttonTitle!))
            : const Text(''),
        leading: buttonLeadingIcon,
        onPressed: onTap,
        isLoading: buttonIsLoading,
        isDisabled: buttonDisabled ?? false,
        size: ButtonSize.fromHeight(buttonHeight) ?? ButtonSize.large,
        expanded: true,
        style: SMButtonStyle(
          textStyle: tokens.typography.typeStyles.subtitle.copyWith(
            fontSize: buttomTitleSize,
            color: buttonTextColor,
          ),
          backgroundColor: buttonBackgroundColor,
        ),
      );
    }
    return null;
  }
}
