import 'package:flutter/material.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

class SMPlusButton extends StatefulWidget {
  final Product product;

  const SMPlusButton({
    super.key,
    required this.product,
  });

  @override
  State<SMPlusButton> createState() => _SMPlusButtonState();
}

class _SMPlusButtonState extends State<SMPlusButton>
    with
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor =
        context.designTokens.productsGridTheme.plusButtonBackgroundColor ??
            tokens.colors.brand.light1.withOpacity(0.4);

    return InkWell(
      onTap: () => _addToBagSizeSelection(),
      child: Container(
        alignment: Alignment.center,
        height: 40,
        width: 40,
        decoration: BoxDecoration(
          color: effectiveBackgroundColor,
          borderRadius:
              context.designTokens.productsGridTheme.plusButtonBorderRadius,
        ),
        child: Icon(icons.addedBag, size: 30),
      ),
    );
  }

  void _addToBagSizeSelection() {
    dispatchSelectContentEvent('compra-rapida:abrir-modal');
    customShowBottomSheet(
      SMBottomSheetAddToBag.forProduct(
        widget.product,
        screenClass: 'PlusButtonPdc',
        initFullLook:
            config.appFeaturesConfig.fullLookConfig.showInAddToBagBottomSheet,
      ),
    );
  }
}
