import 'package:flutter/material.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/components/sm_search_product_image.dart';

class SMOrderItem extends StatefulWidget {
  final ListUserOrder listUserOrder;
  final bool hasRequestedReturn;
  final bool isAvailableToReturn;

  const SMOrderItem({
    super.key,
    required this.listUserOrder,
    this.hasRequestedReturn = false,
    this.isAvailableToReturn = false,
  });

  @override
  State<SMOrderItem> createState() => _SMOrderItemState();
}

class _SMOrderItemState extends State<SMOrderItem>
    with DesignTokensStateMixin, AppRoutesStateMixin, SomaCoreStateMixin {
  @override
  Widget build(BuildContext context) {
    final theme = tokens.orderStatusTimelineTheme;
    final screenSize = MediaQuery.of(context).size;
    final plugins = context.pluginsOfType<OrderItemActionPlugin>();

    OrderStatusData orderStatusData = OrderStatusData(
      context: context,
      orderId: widget.listUserOrder.orderId!,
      trackingUrl: widget.listUserOrder.trackingUrl,
      isPickup: widget.listUserOrder.isPickupOrder,
      invoiceKey: widget.listUserOrder.invoiceKey ?? '',
      approvedPaymentDate: widget.listUserOrder.paymentApprovalDate,
      deliveredDate: theme.deliveryDateFormatter != null
          ? theme
              .deliveryDateFormatter!(widget.listUserOrder.deliveryDate ?? '')
          : DateUtilsSM.monthNumberDayNumber(
              widget.listUserOrder.deliveryDate ?? ''),
      devolutionRoute: null,
      orderConfirmationDate: widget.listUserOrder.orderConfirmationDate,
      listUserOrder: widget.listUserOrder,
    );

    Widget orderStatusChipPlugin = Container(
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(borderRadius.radiusLabel),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0, horizontal: 10.0),
        child: Text(
          textTransform.body(statusLabel),
          style: TextStyle(
            color: TrackOrderUtils.getStatus(widget.listUserOrder).isDelivered
                ? colors.typography.pure2
                : colors.typography.pure1,
            fontFamily: typography.fontFamilyPrimary,
            fontSize: typography.fontSizes.xus,
          ),
        ),
      ),
    );

    try {
      orderStatusChipPlugin = ModuleRegistry.root
          .mapPluginsOfType<OrderStatusPlugin, Widget>((p) =>
              p.build(widget.listUserOrder.isPickupOrder, orderStatusData))
          .elementAt(1);
    } catch (e) {
      debugPrint(e.toString());
    }

    String orderDate = context
            .designTokens.orderDetailsTheme.estimateDeliveryAfterInvoiceKey
        ? DateUtilsSM.convertToBrasiliaUTC(
                DateTime.parse(widget.listUserOrder.creationDate.toString()))
            .toString()
        : widget.listUserOrder.creationDate.toString();

    return GestureDetector(
      onTap: () {
        widget.listUserOrder.setCanBeReturned(widget.isAvailableToReturn);
        widget.listUserOrder.setHasRequestedReturn(widget.hasRequestedReturn);

        goToTrackOrderRoute(widget.listUserOrder);
      },
      child: Container(
        width: double.infinity,
        decoration: const BoxDecoration(color: Colors.white),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.listUserOrder.orderId!,
                        style: typography.typeStyles.subtitle),
                    Text(DateUtilsSM.dayNumberMonthText(orderDate),
                        style: typography.typeStyles.body),
                  ],
                ),
                Icon(icons.arrowRight, size: 30)
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: tokens.spacingStack.xs),
              child: orderStatusChipPlugin,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${widget.listUserOrder.items!.length.toString()} ${widget.listUserOrder.items!.length > 1 ? ' produtos' : 'produto'}',
                  style: typography.typeStyles.body,
                ),
                Text(
                  CurrencyUtils.format(
                    amount: widget.listUserOrder.value,
                    dividedBy100: true,
                  ),
                  style: typography.typeStyles.body,
                ),
              ],
            ),
            if (config.appFeaturesConfig.trackingUrl == true) ...[
              ...widget.listUserOrder.trackingUrl.map((e) => GestureDetector(
                    onTap: () => navigateToNamed(webviewRoute,
                        arguments: WebViewParams(
                          url: e.replaceAll('http://', 'https://'),
                          screenName: 'rastrear entrega',
                          allowJavascript: true,
                        )),
                    child: Padding(
                      padding: EdgeInsets.only(top: spacingStack.xs),
                      child: Text(
                        'rastrear entrega',
                        style: typography.typeStyles.body.copyWith(
                          color: colors.brand.pure1,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  )),
            ],
            Padding(
              padding: EdgeInsets.only(
                  top: config.appFeaturesConfig.trackingUrl == true &&
                          widget.listUserOrder.hasTrackingUrl
                      ? spacingStack.md
                      : spacingStack.lg),
              child: SizedBox(
                height: 130,
                width: screenSize.width,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: widget.listUserOrder.items?.length,
                  itemBuilder: (BuildContext context, int index) {
                    final item = widget.listUserOrder.items?[index];

                    return Padding(
                      padding: EdgeInsets.only(right: tokens.spacingInline.xs),
                      child: SMSearchProductImage(
                        imageUrl: item?.imageUrl as String,
                        productId: item?.productId as String,
                      ),
                    );
                  },
                ),
              ),
            ),
            ...plugins
                .map((p) => p.buildReturnButton(
                      context,
                      widget.listUserOrder,
                      widget.hasRequestedReturn,
                      widget.isAvailableToReturn,
                    ))
                .whereType<Widget>(),
          ],
        ),
      ),
    );
  }

  String get statusLabel {
    var orderStatusLabel = tokens.orderStatusTheme.labelFromStatusLabelList(
        TrackOrderUtils.getStatus(widget.listUserOrder));
    assert(
      orderStatusLabel != null,
      'O ${TrackOrderUtils.getStatus(widget.listUserOrder)} não possui legenda disponível',
    );
    return orderStatusLabel ?? '';
  }

  Color get statusColor {
    var orderStatusColor = tokens.orderStatusTheme.colorFromStatusLabelList(
        TrackOrderUtils.getStatus(widget.listUserOrder));
    assert(
      orderStatusColor != null,
      'O ${TrackOrderUtils.getStatus(widget.listUserOrder)} não possui cor disponível',
    );
    return orderStatusColor ?? tokens.colors.neutral.light2;
  }
}

abstract class OrderItemActionPlugin extends Plugin {
  Widget? buildReturnButton(BuildContext context, ListUserOrder listUserOrder,
      bool isReturned, bool canBeReturned);
}
