import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:soma_core/modules/auth/models/user_orders/gift_card.dart';
import 'package:soma_core/modules/auth/models/user_orders/item.dart';
import 'package:soma_core/modules/auth/models/user_orders/invoice_data.dart';
import 'package:soma_core/modules/auth/models/user_orders/selected_address.dart';
import 'package:soma_core/modules/catalog/models/product/status_order.dart';
import 'package:soma_core/modules/checkout/models/orderForm/produtc_order_form.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/selected_delivery.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/cms/models/cms_order_details_document.dart';
import 'package:soma_ui/widgets/components/sm_order_details_sections.dart';

import '../../soma_ui.dart';
import '../templates/checkout/orderConfirmation/sm_confirmed_order_details.dart';

class SMOrderDetails extends StatefulWidget {
  final OrderStatus status;
  final bool isDelivered;
  final List<GiftCard> listGiftCardItem;
  final List<Item> userOrderItems;
  final bool? hasOrderResumeHeader;
  final bool? showShowcase;
  final String deliveryDate;
  final String cancelationDate;
  final String typeDelivery;
  final String statusDeliveryDate;
  final String statusConfirmedOrderDate;
  final String statusApprovedPaymentDate;
  final String statusOutForDeliveryDate;
  final String? cancelationReason;
  final String address;
  final String addressComplement;
  final String paymentMethod;
  final String paymentTitle;
  final String priceDelivery;
  final String installments;
  final String totalValue;
  final String? title;
  final bool hasFooter;
  final String? subtitle;
  final String? orderGroup;
  final String? valueDiscount;
  final String? faqRouteUrl;
  final String? devolutionRouteUrl;
  final void Function()? trackOrderAction;
  final ImageProvider? image;
  final String orderId;
  final bool isPickupOrder;
  final PaymentMethodOptions? paymentMethodOption;
  final DateTime? dateOutForDeliveryDate;
  final DateTime? dateApprovedPaymentDate;
  final InvoiceData? invoiceData;
  final String? invoiceKey;
  final String? embeddedInvoiceUrl;
  final bool? combineAddressSection;
  final String? receiverName;
  final String? selectedAddress;
  final bool cameFromConfirmationOrder;
  final Color? backgroundColor;
  final bool showCheckoutResume;
  final EdgeInsets? addressSectionPadding;
  final EdgeInsets? deliveryResumePadding;
  final EdgeInsets? addressInfoPadding;
  final ListUserOrder? listUserOrder;

  const SMOrderDetails({
    super.key,
    this.faqRouteUrl,
    this.devolutionRouteUrl,
    required this.cancelationDate,
    this.cancelationReason,
    required this.status,
    this.isDelivered = false,
    required this.userOrderItems,
    required this.listGiftCardItem,
    this.image,
    this.title,
    this.subtitle,
    this.orderGroup,
    this.hasOrderResumeHeader = false,
    this.showShowcase = false,
    this.valueDiscount,
    this.trackOrderAction,
    required this.deliveryDate,
    required this.statusDeliveryDate,
    required this.statusConfirmedOrderDate,
    required this.statusApprovedPaymentDate,
    required this.statusOutForDeliveryDate,
    required this.typeDelivery,
    required this.address,
    required this.addressComplement,
    required this.paymentMethod,
    required this.priceDelivery,
    required this.paymentTitle,
    required this.installments,
    required this.totalValue,
    required this.orderId,
    this.listUserOrder,
    this.isPickupOrder = false,
    this.paymentMethodOption,
    this.dateOutForDeliveryDate,
    this.hasFooter = true,
    this.dateApprovedPaymentDate,
    this.invoiceData,
    this.invoiceKey,
    this.embeddedInvoiceUrl,
    this.combineAddressSection = false,
    this.receiverName,
    this.selectedAddress,
    this.cameFromConfirmationOrder = false,
    this.backgroundColor,
    this.showCheckoutResume = true,
    this.addressSectionPadding,
    this.deliveryResumePadding,
    this.addressInfoPadding,
  });

  static const fromListUserOrder = _OrderDetailsFromListUserOrder.new;

  @override
  State<StatefulWidget> createState() => _SMOrderDetailsState();
}

class _SMOrderDetailsState extends State<SMOrderDetails>
    with DesignTokensStateMixin, SomaCoreStateMixin, AppRoutesStateMixin {
  List<Product> suggestedProducts = [];
  bool isLoading = false;

  late Widget? returnButtonPlugin;
  late OrderReturnsResumePlugin? plugin;

  Future<void> _loadSuggestedProducts() async {
    try {
      List<Product> response;
      response = await catalogController.getProductsByClusterId(clusterId: 689);

      setState(() => suggestedProducts = response);
    } catch (err) {
      debugPrint('Não foi possivel carregar os produtos sugeridos');
    }
  }

  @override
  void initState() {
    super.initState();
    _loadSuggestedProducts();
  }

  @override
  Widget build(BuildContext context) {
    final plugin = ModuleRegistry.root
        .pluginsOfType<OrderDetailsSectionPlugin>()
        .firstOrNull;

    final checkingAccountBalanceInfo = ModuleRegistry.root
        .mapPluginsOfType<BalanceFeedbackOrdeDatils, Widget>(
            (p) => p.build(context))
        .firstOrNull;

    returnButtonPlugin = plugin?.build(
        context,
        widget.listUserOrder,
        widget.listUserOrder?.hasRequestedReturn ?? false,
        widget.listUserOrder?.canBeReturned ?? false);
    final selectedDelivery = addressController.selectedDelivery;

    String deliveryText = textTransform.body(
        ('${widget.typeDelivery}: ${widget.priceDelivery}')
            .replaceFirst("entrega ", ""));

    Color? deliveryTextStyleColor;
    if (tokens.orderDetailsTheme.estimateDeliveryAfterInvoiceKey == true &&
        (selectedDelivery?.selectedStore?.isPickupStore ??
            widget.isPickupOrder)) {
      deliveryTextStyleColor = tokens.colors.typography.pure2;
      deliveryText = deliveryText.replaceFirst(":", " -");
      deliveryText = deliveryText.replaceFirst("g", "G");
    }

    return SMCmsContent.provideForDocument<CmsOrderDetailsDocument>(
      'order-details',
      documentParser: CmsOrderDetailsDocument.fromJson,
      child: Container(
        color: widget.backgroundColor ?? Colors.transparent,
        child: Column(
          children: [
            if (appFeaturesConfig.newTrackOrderHeader) ...[
              if (widget.cancelationDate != '') ...[
                Container(
                  color: colors.feedback.lightError,
                  margin: EdgeInsets.only(
                    left: spacingStack.sm,
                    right: spacingStack.sm,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
                    child: SizedBox(
                      width: double.infinity,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Infelizmente o seu pedido foi cancelado',
                            style: tokens.typography.typeStyles.body.copyWith(
                              color: colors.feedback.pureError,
                            ),
                          ),
                          SizedBox(height: spacingStack.md),
                        ],
                      ),
                    ),
                  ),
                )
              ] else
                Container(
                  color: colors.neutral.light1,
                  margin: EdgeInsets.only(
                    left: spacingStack.sm,
                    right: spacingStack.sm,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
                    child: SizedBox(
                      width: double.infinity,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widget.listUserOrder != null)
                            _DeliveryText(
                                listUserOrder: widget.listUserOrder!,
                                deliveryText: deliveryText,
                                deliveryTextStyleColor: deliveryTextStyleColor),
                          if (!(selectedDelivery
                                  ?.selectedStore?.isPickupStore ??
                              widget.isPickupOrder)) ...[
                            SizedBox(height: spacingStack.xs),
                            orderDetailsTheme.estimateDeliveryAfterInvoiceKey &&
                                    widget.listUserOrder != null
                                ? DelivereEstimateText(
                                    order: widget.listUserOrder!)
                                : Text(
                                    getTrackOrderDeliveryText(),
                                    style: tokens.typography.typeStyles.body
                                        .copyWith(
                                            color: colors.typography.pure2),
                                  ),
                          ],
                          SizedBox(height: spacingStack.md),
                        ],
                      ),
                    ),
                  ),
                )
            ] else ...[
              Padding(
                padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
                child: SmOrderDeliveryResume(
                  status: widget.status,
                  isDelivered: widget.isDelivered,
                  deliveryDate: widget.deliveryDate,
                  cancelationDate: widget.cancelationDate,
                  deliveryPrice: widget.priceDelivery,
                  deliveryType: widget.typeDelivery,
                  isPickupStore:
                      selectedDelivery?.selectedStore?.isPickupStore ??
                          widget.isPickupOrder,
                  orderId: widget.orderId,
                  value: widget.totalValue,
                  deliveryResumePadding: widget.deliveryResumePadding,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: spacingStack.sm,
                  horizontal: spacingInline.sm,
                ),
                child: SMLine(
                  lineSize: LineSize.small,
                  lineColor: colors.neutral.medium1,
                ),
              ),
            ],
            if (widget.cancelationReason.isNotNullOrEmpty)
              _OrderCancelledSection(widget.cancelationReason!),
            if (widget.orderGroup != null)
              Padding(
                padding: EdgeInsets.only(
                  left: spacingInline.md,
                  right: spacingInline.sm,
                  top: spacingStack.xl,
                ),
                child: Text(
                  widget.orderGroup!,
                  style: TextStyle(
                    fontWeight: FontWeight.w800,
                    fontFamily: tokens.typography.fontFamilyPrimary,
                    color: const Color(0xFF565656),
                    fontSize: typography.fontSizes.xxs,
                  ),
                ),
              ),
            SMCmsContent.provided<CmsOrderDetailsDocument>(
              selector: (document) => [
                document.attributes.textWarning,
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: spacingInline.sm,
                vertical: spacingStack.md,
              ),
              child: SMOrderStatus(
                orderId: widget.orderId,
                confirmedOrderDate: widget.statusConfirmedOrderDate,
                approvedPaymentDate: widget.statusApprovedPaymentDate,
                outForDeliveryDate: widget.statusOutForDeliveryDate,
                dateOutForDeliveryDate: widget.dateOutForDeliveryDate,
                dateApprovedPaymentDate: widget.dateApprovedPaymentDate,
                deliveredDate: widget.statusDeliveryDate,
                isPickupOrder: widget.isPickupOrder,
                trackingUrl: widget.listUserOrder?.trackingUrl,
                invoiceKey: widget.invoiceKey,
                devolutionRoute:
                    (widget.listUserOrder?.canRequestDevolution ?? false) &&
                            widget.devolutionRouteUrl != null
                        ? widget.devolutionRouteUrl
                        : null,
                listUserOrder: widget.listUserOrder,
              ),
            ),
            if (returnButtonPlugin != null &&
                tokens.orderDetailsTheme.showDevolutionPluginOption) ...[
              Padding(
                padding: EdgeInsets.symmetric(vertical: spacingStack.xs),
                child: SizedBox(
                  height: 1,
                  child: SMLine(
                    lineSize: LineSize.small,
                    lineColor: colors.neutral.medium1,
                  ),
                ),
              ),
              returnButtonPlugin!
            ],
            Padding(
              padding:
                  orderDetailsTheme.productCardTheme?.resumeHeaderPadding ??
                      EdgeInsets.symmetric(
                        vertical: spacingStack.sm,
                        horizontal: spacingInset.md,
                      ),
              child: _OrderResumeHeader(
                itemsLength: widget.userOrderItems.length,
                embeddedInvoiceUrl: widget.embeddedInvoiceUrl,
              ),
            ),
            Padding(
              padding: widget.cameFromConfirmationOrder
                  ? EdgeInsets.zero
                  : orderDetailsTheme.productCardTheme?.contentPadding ??
                      EdgeInsets.symmetric(
                        vertical: spacingStack.xl,
                      ),
              child: _UserOrderItems(
                userOrderItems: widget.userOrderItems,
                onTapProduct: _onTapProduct,
              ),
            ),
            SmOrderDetailsSection(
              cameFromConfirmationOrder: widget.cameFromConfirmationOrder,
              address: widget.address,
              addressComplement: widget.addressComplement,
              typeDelivery: widget.typeDelivery,
              priceDelivery: widget.priceDelivery,
              listGiftCardItem: widget.listGiftCardItem,
              paymentTitle: widget.paymentTitle,
              paymentMethod: widget.paymentMethod,
              installments: widget.installments,
              isDelivered: widget.isDelivered,
              deliveryDate: widget.deliveryDate,
              status: widget.status,
              cancelationDate: widget.cancelationDate,
              isPickupOrder: widget.isPickupOrder,
              addressInfoPadding: widget.addressInfoPadding,
              addressSectionPadding: widget.addressSectionPadding,
              combineAddressSection: widget.combineAddressSection,
              devolutionRouteUrl: widget.devolutionRouteUrl,
              paymentMethodOption: widget.paymentMethodOption,
              receiverName: widget.receiverName,
              selectedAddress: widget.selectedAddress,
              selectedDelivery: selectedDelivery,
              listUserOrder: widget.listUserOrder,
              backgroundColor: orderDetailsTheme.addressBackgroundColor,
              showDividerAfterAddressInformations:
                  orderDetailsTheme.showDividerAfterAddressInformations,
            ),
            if (orderDetailsTheme.showDeliverySLAAfterAddressInformations) ...[
              Container(
                color: colors.neutral.pure1,
                child: Column(
                  children: [
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: spacingInline.sm),
                      child: SMListItem(
                        titleOrder: ListItemTitleOrder.subtitleThenTitle,
                        title: Text('Chega até ${widget.deliveryDate}'),
                        subtitle: Text(
                          tokens.textTransform.body(
                              '${widget.typeDelivery} • ${widget.priceDelivery}'),
                          style: tokens.typography.typeStyles.body,
                        ),
                      ),
                    ),
                    SmHorizontalDivider(
                      horizontalSpacing: spacingInline.sm,
                      topSpacing: spacingStack.md,
                      bottomSpacing: 0,
                    ),
                  ],
                ),
              )
            ],
            if (widget.showCheckoutResume)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 0),
                child: SMCheckoutResume(
                  textProducts: widget.userOrderItems.length > 1
                      ? '${widget.userOrderItems.length.toString()} produtos'
                      : '${widget.userOrderItems.length.toString()} produto',
                  textValues: widget.totalValue,
                  textTotalValues: widget.installments,
                  textDelivery: 'entrega',
                  textDateDelivery: widget.priceDelivery,
                  giftCards: widget.listGiftCardItem,
                  textValueDiscount: widget.valueDiscount ?? '',
                ),
              ),
            if (checkingAccountBalanceInfo != null) ...[
              checkingAccountBalanceInfo
            ],
            if (widget.hasFooter)
              SmOrderDetailsFooter(
                faqRouteUrl: widget.faqRouteUrl!,
                devolutionRouteUrl: widget.devolutionRouteUrl,
                showShowcase: widget.showShowcase!,
                suggestedProducts: suggestedProducts,
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _onTapProduct(String? productId) async {
    try {
      final id = int.tryParse(productId ?? '') ?? 0;
      Product product = await catalogController.getProductById(
        productId: id,
        loadSimilarProducts: false,
      );
      catalogController.selectedProduct(product);
      navigateToNamed(routes.productDetails);
    } catch (e, st) {
      debugPrint('Error while getting user orders: $e');
      debugPrintStack(stackTrace: st);
    }
  }

  String getTrackOrderDeliveryText() {
    if (widget.listUserOrder == null) return '';
    final isDelivered = TrackOrderUtils.isDelivered(widget.listUserOrder!);

    final date = TrackOrderUtils.getDeliveryDate(
      listUserOrder: widget.listUserOrder,
      dateFormatter: DateUtilsSM.dayNumberMonthNumberYearNumber,
      estimateDeliveryAfterInvoiceKey:
          orderDetailsTheme.estimateDeliveryAfterInvoiceKey,
    );
    final text =
        '${isDelivered ? 'Pedido entregue em: ' : 'Previsão de entrega: '}$date';
    return text;
  }
}

class DelivereEstimateText extends StatelessWidget {
  const DelivereEstimateText({
    required this.order,
    super.key,
  });
  final ListUserOrder order;

  @override
  Widget build(BuildContext context) {
    final plugin = ModuleRegistry.root
        .pluginsOfType<OrderStatusDeliveredPlugin>()
        .firstOrNull;

    return plugin?.build(order, false, null) ?? const SizedBox.shrink();
  }
}

class _DeliveryText extends StatelessWidget {
  const _DeliveryText({
    required this.listUserOrder,
    required this.deliveryText,
    required this.deliveryTextStyleColor,
  });
  final ListUserOrder listUserOrder;
  final String deliveryText;
  final Color? deliveryTextStyleColor;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final plugin = ModuleRegistry.root
        .pluginsOfType<PickupTrackOrderDeliveryText>()
        .firstOrNull;

    if (listUserOrder.isPickupOrder &&
        tokens.orderDetailsTheme.estimateDeliveryAfterInvoiceKey &&
        plugin != null) {
      return plugin.build(OrderStatusData(
        context: context,
        listUserOrder: listUserOrder,
        orderId: listUserOrder.orderId ?? '',
        invoiceKey: listUserOrder.invoiceKey ?? '',
        isPickup: listUserOrder.isPickupOrder,
        defaultTrackOrder: deliveryText,
      ));
    }

    return Text(
      deliveryText,
      style: tokens.typography.typeStyles.body
          .copyWith(color: deliveryTextStyleColor),
    );
  }
}

class _RowPayment extends StatelessWidget {
  final String paymentMethod;
  final String paymentTitle;
  final String installments;
  final bool cameFromConfirmationOrder;

  const _RowPayment({
    required this.paymentMethod,
    required this.paymentTitle,
    required this.installments,
    required this.cameFromConfirmationOrder,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final theme = tokens.orderDetailsTheme.rowPaymentTheme;

    return Padding(
      padding: cameFromConfirmationOrder && theme?.contentPadding != null
          ? theme!.contentPadding!
          : EdgeInsets.only(bottom: tokens.spacingStack.sm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!cameFromConfirmationOrder) ...[
                Text(
                  paymentTitle,
                  style: tokens.typography.typeStyles.bodyXs?.copyWith(
                    color: tokens.colors.typography.light2,
                  ),
                ),
                SizedBox(height: tokens.spacingStack.xxs),
              ],
              Text(
                paymentMethod,
                style: tokens.typography.typeStyles.bodyMd?.copyWith(
                  color: tokens.colors.typography.dark3,
                ),
              ),
              if (cameFromConfirmationOrder) ...[
                SizedBox(height: tokens.spacingStack.xs),
                Text(
                  installments,
                  style: tokens.typography.typeStyles.bodySm.copyWith(
                    color: tokens.colors.typography.dark3,
                  ),
                )
              ],
            ],
          ),
          if (!cameFromConfirmationOrder)
            Text(
              installments,
              style: tokens.typography.typeStyles.bodySm.copyWith(
                color: tokens.colors.typography.pure2,
              ),
            ),
        ],
      ),
    );
  }
}

class _OrderCancelledSection extends StatelessWidget {
  const _OrderCancelledSection(this.cancellationReason);

  final String cancellationReason;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    return Column(
      children: [
        Container(
          alignment: Alignment.center,
          height: 65,
          width: MediaQuery.of(context).size.width,
          color: tokens.colors.feedback.pureError,
          margin: EdgeInsets.symmetric(
            vertical: tokens.spacingStack.sm,
            horizontal: tokens.spacingInline.sm,
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: tokens.spacingStack.sm,
              horizontal: tokens.spacingInline.sm,
            ),
            child: Text(
              'Seu pedido foi cancelado pelo motivo "$cancellationReason"',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: tokens.typography.fontFamilyPrimary,
                color: tokens.colors.neutral.pure1,
                fontSize: tokens.typography.fontSizes.us,
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: tokens.spacingStack.sm,
            horizontal: tokens.spacingInline.sm,
          ),
          child: SMLine(
            lineSize: LineSize.small,
            lineColor: tokens.colors.neutral.medium1,
          ),
        ),
      ],
    );
  }
}

class _OrderDetailsFromListUserOrder extends StatelessWidget {
  const _OrderDetailsFromListUserOrder(
    this.listUserOrder, {
    required this.faqRouteUrl,
    required this.devolutionRouteUrl,
  });

  final ListUserOrder listUserOrder;
  final String? faqRouteUrl;
  final String? devolutionRouteUrl;

  OrderStatus getStatus(StatusByPhone2businessResponse statusByPhone2business) {
    final status = (statusByPhone2business.data
            .where((status) => status.date != null)
            .isNotEmpty)
        ? statusByPhone2business.data
                .where((status) => status.date != null)
                .lastOrNull
                ?.id ??
            ''
        : '';

    return TrackOrderUtils.getStatusByPhone2Business(status);
  }

  bool isDeliveredPhone2Business(
      StatusByPhone2businessResponse statusByPhone2business) {
    return getStatus(statusByPhone2business) == OrderStatus.delivered;
  }

  @override
  Widget build(BuildContext context) {
    final config = context.locateService<Config>();
    final tokens = context.designTokens;
    final trackOrderTheme = tokens.trackOrderTheme;
    final statusByPhone2business =
        SMTrackOrderScope.of(context)?.trackOrderState.statusByPhone2business;
    final hasCancellationReason =
        trackOrderTheme.showCancellationReason == true &&
            listUserOrder.cancellationReason != null;
    final cancellationDate = listUserOrder.cancellationData?.cancelationDate;
    final bool isOrderCancelled =
        TrackOrderUtils.getStatus(listUserOrder) == OrderStatus.canceled &&
            cancellationDate != null;
    final payments =
        listUserOrder.paymentData?.transactions?.firstOrNull?.payments ?? [];
    final orderPackages = listUserOrder.packageAttachment?.packages ?? [];
    final selectedSlaId = listUserOrder
        .shippingData?.logisticsInfo?.firstOrNull?.selectedSlaId!
        .toLowerCase();
    final isPickupStore = listUserOrder._selectedStore?.isPickupStore ?? false;
    final receiverName = listUserOrder._selectedAdress?.receiverName ?? '';
    final userAddress =
        '${listUserOrder._selectedAdress?.street ?? ''}, ${listUserOrder._selectedAdress?.number ?? ''} • ${listUserOrder._selectedAdress?.postalCode ?? ''}';
    final orderStatusTimelineTheme = tokens.orderStatusTimelineTheme;

    return SMOrderDetails(
      cancelationDate:
          isOrderCancelled ? DateUtilsSM.fullDate(cancellationDate) : '',
      cancelationReason:
          hasCancellationReason ? listUserOrder.cancellationReason! : '',
      status: statusByPhone2business != null
          ? getStatus(statusByPhone2business)
          : TrackOrderUtils.getStatus(listUserOrder),
      userOrderItems: listUserOrder.items ?? [],
      listGiftCardItem: TrackOrderUtils.getAllGiftCards(payments),
      deliveryDate: listUserOrder._deliveryDate,
      statusDeliveryDate: TrackOrderUtils.getDeliveredDate(
        order: listUserOrder,
        dateFormatter: orderStatusTimelineTheme.deliveryDateFormatter,
      ),
      statusConfirmedOrderDate: DateUtilsSM.dateAndHour(
        listUserOrder.creationDate.toString(),
      ),
      statusApprovedPaymentDate: DateUtilsSM.dateAndHour(
        listUserOrder.authorizedDate.toString(),
      ),
      statusOutForDeliveryDate: orderPackages.isNotEmpty
          ? DateUtilsSM.dateAndHour(
              orderPackages.first.issuanceDate.toString(),
            )
          : '',
      dateOutForDeliveryDate:
          DateTime.tryParse(orderPackages.firstOrNull?.issuanceDate ?? ''),
      dateApprovedPaymentDate:
          DateTime.tryParse(listUserOrder.authorizedDate.toString()),
      typeDelivery:
          isPickupStore ? 'retirar na loja' : 'entrega $selectedSlaId',
      address: isPickupStore
          ? '${listUserOrder._selectedStore?.address?.asString ?? ''} • $receiverName'
          : '$userAddress • $receiverName',
      addressComplement: isPickupStore
          ? listUserOrder._pickUpAddressComplement
          : '${listUserOrder._selectedAdress?.complement ?? ''} • ${listUserOrder._selectedAdress?.neighborhood ?? ''}',
      paymentMethod: TrackOrderUtils.getPaymentsControllerTitle(
        paymentMethod: listUserOrder.paymentMethod,
        cardName: listUserOrder.paymentCreditCard?.paymentSystemName,
        cardFinalNumber: listUserOrder.paymentCreditCard?.lastDigits,
        paymentValue: listUserOrder.value,
        giftCards: listUserOrder.paymentData?.giftCards,
        config: config,
      ),
      priceDelivery: TrackOrderUtils.getPriceDelivery(listUserOrder),
      paymentTitle: TrackOrderUtils.getPaymentsControllerSubTitle(
        paymentMethod: listUserOrder.paymentMethod,
        giftCards: listUserOrder.paymentData?.giftCardsInUse(config) ?? [],
        config: config,
      ),
      installments: TrackOrderUtils.getInstallments(listUserOrder),
      totalValue: listUserOrder.totals?.firstOrNull?.value != null
          ? CurrencyUtils.format(
              amount: listUserOrder.totals?.firstOrNull?.value,
              dividedBy100: true,
            )
          : '',
      orderId: listUserOrder.orderId ?? '',
      isDelivered: statusByPhone2business != null
          ? isDeliveredPhone2Business(statusByPhone2business)
          : TrackOrderUtils.isDelivered(listUserOrder),
      faqRouteUrl: faqRouteUrl,
      devolutionRouteUrl: devolutionRouteUrl,
      valueDiscount: listUserOrder._valueDiscount,
      isPickupOrder: listUserOrder.isPickupOrder,
      paymentMethodOption: listUserOrder.paymentMethod,
      invoiceData: listUserOrder.invoiceData,
      invoiceKey: listUserOrder.invoiceKey,
      embeddedInvoiceUrl: listUserOrder
          .packageAttachment?.packages?.firstOrNull?.embeddedInvoice,
      listUserOrder: listUserOrder,
    );
  }
}

class _OrderResumeHeader extends StatelessWidget {
  const _OrderResumeHeader(
      {required this.itemsLength, this.embeddedInvoiceUrl});

  final int itemsLength;
  final String? embeddedInvoiceUrl;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tokens.textTransform.body('resumo do pedido'),
            style: tokens.orderDetailsTheme.orderResumeTitleTextStyle ??
                tokens.typography.typeStyles.subtitle,
          ),
          Text(
            itemsLength > 1
                ? '${itemsLength.toString()} produtos'
                : '${itemsLength.toString()} produto',
            style: tokens.typography.typeStyles.body,
          ),
        ],
      ),
    );
  }
}

class _UserOrderItems extends StatelessWidget {
  const _UserOrderItems({
    required this.userOrderItems,
    required this.onTapProduct,
  });

  final List<Item> userOrderItems;
  final ValueChanged<String> onTapProduct;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final theme = context.designTokens.orderDetailsTheme.productCardTheme;
    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: userOrderItems.length,
      itemBuilder: (BuildContext context, int index) {
        return SMProductCard(
          product: ProdutcOrderForm.fromOrderItem(
            userOrderItems[index],
          ),
          widthImageSize: theme?.productCardSize?.imageWidth ??
              tokens.sizes.bagProductCardWidthImageSize,
          heightImageSize: theme?.productCardSize?.imageHeight ??
              tokens.sizes.bagProductCardHeightImageSize,
          heightSizeButton: 40.0,
          widthSizeButton: 40.0,
          titlePadding: theme?.titlePadding,
          bottomPadding: 5.0,
          showProductSize: true,
          showProductQuantity: true,
          isBagProduct: userOrderItems.isEmpty,
          onTapProductImage: () =>
              onTapProduct(userOrderItems[index].productId ?? ''),
          onTapTitle: () => onTapProduct(userOrderItems[index].productId ?? ''),
        );
      },
    );
  }
}

class OrderPaymentSection extends StatelessWidget {
  const OrderPaymentSection({
    super.key,
    required this.listGiftCardItem,
    required this.paymentMethodOptions,
    required this.paymentMethod,
    required this.paymentTitle,
    required this.installments,
    required this.cameFromConfirmationOrder,
  });

  final List<GiftCard> listGiftCardItem;
  final PaymentMethodOptions? paymentMethodOptions;
  final String paymentMethod;
  final String paymentTitle;
  final String installments;
  final bool cameFromConfirmationOrder;

  @override
  Widget build(BuildContext context) {
    final config = context.locateService<Config>();
    final tokens = context.designTokens;
    final isRowPayment = context.designTokens.orderDetailsTheme.rowPayment;

    Widget? checkingAccountPaymentPlugin;
    final giftCards = listGiftCardItem;
    final cashbackProvider = config.appFeaturesConfig.cashBackConfig?.provider;
    final isCreditVoucher =
        config.appFeaturesConfig.creditVoucherConfig?.isEnabled ?? false;
    final checkingAccountProvider =
        config.appFeaturesConfig.checkingAccountConfig?.provider;
    final isCheckingAccount = checkingAccountProvider != null &&
        giftCards.firstOrNull?.provider == checkingAccountProvider;
    if (giftCards.isNotEmpty) {
      checkingAccountPaymentPlugin = ModuleRegistry.root
          .mapPluginsOfType<ConfirmedOrderPaymentPlugin, Widget>(
            (p) => p.build(context, giftCards.first),
          )
          .firstOrNull;
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: tokens.spacingStack.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (checkingAccountPaymentPlugin != null)
            checkingAccountPaymentPlugin,
          if ((paymentMethodOptions != PaymentMethodOptions.giftCard &&
                  !isRowPayment) ||
              (paymentMethodOptions == PaymentMethodOptions.giftCard &&
                  !isCheckingAccount &&
                  !isRowPayment))
            Padding(
              padding: EdgeInsets.only(bottom: tokens.spacingStack.sm),
              child: SMListItem(
                titleOrder: ListItemTitleOrder.subtitleThenTitle,
                title: Text(tokens.textTransform.body(paymentMethod)),
                subtitle: Text(tokens.textTransform.body(paymentTitle)),
                details: Text(tokens.textTransform.body(installments)),
              ),
            ),
          if ((paymentMethodOptions != PaymentMethodOptions.giftCard &&
                  isRowPayment) ||
              (paymentMethodOptions == PaymentMethodOptions.giftCard &&
                  !isCheckingAccount &&
                  isRowPayment)) ...[
            if (cameFromConfirmationOrder)
              Padding(
                padding: EdgeInsets.only(bottom: tokens.spacingStack.xs),
                child: Text(
                  tokens.textTransform.body(
                    TrackOrderUtils.getPaymentsControllerSubTitle(
                      paymentMethod: paymentMethodOptions,
                      giftCards: giftCards,
                      config: config,
                    ),
                  ),
                  style: tokens.typography.typeStyles.bodySm.copyWith(
                    color: tokens.colors.typography.light1,
                  ),
                ),
              ),
            _RowPayment(
              paymentMethod: paymentMethod,
              paymentTitle: paymentTitle,
              installments: installments,
              cameFromConfirmationOrder: cameFromConfirmationOrder,
            ),
          ],
          if (!isCheckingAccount && giftCards.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: tokens.spacingStack.sm),
              child: ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: listGiftCardItem.length,
                itemBuilder: (BuildContext context, int index) {
                  final giftCard = listGiftCardItem[index];
                  final isCashback = cashbackProvider != null &&
                      giftCard.provider == cashbackProvider;

                  final String redemptionCode;
                  if (isCashback) {
                    redemptionCode = 'cashback';
                  } else if (isCreditVoucher) {
                    redemptionCode =
                        giftCard.caption ?? giftCard.redemptionCode ?? '';
                  } else {
                    redemptionCode = giftCard.redemptionCode ?? '';
                  }

                  return SMGiftCardLabel(
                    first: index == 0,
                    price: CurrencyUtils.format(
                        amount: giftCard.value, dividedBy100: true),
                    redemptionCode: redemptionCode,
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

class SmOrderDeliveryResume extends StatelessWidget {
  final OrderStatus status;
  final bool isDelivered;
  final String deliveryDate;
  final String cancelationDate;
  final String deliveryType;
  final String deliveryPrice;
  final bool isPickupStore;
  final bool isDigital;
  final String? value;
  final String? orderId;
  final EdgeInsets? deliveryResumePadding;

  const SmOrderDeliveryResume({
    super.key,
    required this.status,
    required this.isDelivered,
    required this.deliveryDate,
    required this.cancelationDate,
    required this.deliveryType,
    required this.deliveryPrice,
    required this.isPickupStore,
    this.isDigital = false,
    this.value,
    this.orderId,
    this.deliveryResumePadding,
  });

  String _deliveryTitle(DesignTokens tokens) {
    final pickupDayText = tokens.termsAndMessages.orderReviewPickupDateText;
    String deliveryTitle = '';
    if (isDigital) return deliveryDate;
    if (isDelivered) {
      deliveryTitle = deliveryDate.split(' ').elementAtOrNull(1) ?? '';
    } else if (status == OrderStatus.canceled) {
      deliveryTitle = cancelationDate;
    } else if (isPickupStore) {
      if (pickupDayText != null) {
        deliveryTitle = '$pickupDayText $deliveryDate';
      } else {
        deliveryTitle = 'Hoje até $deliveryDate';
      }
    } else if (deliveryDate.isNotEmpty) {
      deliveryTitle = 'Chega até $deliveryDate';
    }
    return tokens.textTransform.body(deliveryTitle);
  }

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    final currentStausByPhone2business = SMTrackOrderScope.of(context)
        ?.trackOrderState
        .currenteStatusByPhone2Business;

    return Column(
      children: [
        Padding(
          padding: deliveryResumePadding ??
              EdgeInsets.symmetric(vertical: tokens.spacingStack.lg),
          child: SMListItem(
            titleOrder: ListItemTitleOrder.subtitleThenTitle,
            title: Text(tokens.textTransform.body(_deliveryTitle(tokens))),
            subtitle: Text(
              isDelivered
                  ? 'pedido entregue'
                  : status == OrderStatus.canceled
                      ? 'pedido cancelado em'
                      : deliveryPrice.isNotEmpty
                          ? '$deliveryType • $deliveryPrice'
                          : deliveryType,
            ),
            leading: Icon(
              status == OrderStatus.canceled
                  ? tokens.icons.boxCancel
                  : status == OrderStatus.delivered
                      ? tokens.icons.boxCheck
                      : status == OrderStatus.approvedPayment
                          ? tokens.icons.calendar
                          : tokens.icons.calendarHome,
            ),
          ),
        ),
        if (currentStausByPhone2business ==
                StatusOrderByPhone2Business.awaitingPickup &&
            value != null &&
            orderId != null) ...[
          SmQrcodePickup(value: value!, orderId: orderId!),
          SizedBox(
            height: tokens.spacingStack.sm,
          )
        ],
      ],
    );
  }
}

extension on ListUserOrder {
  SelectedAddress? get _selectedAdress =>
      shippingData?.selectedAddresses?.firstOrNull;
  PickupStore? get _selectedStore => selectedDelivery?.selectedStore;
  String get _addressFriendlyName => _selectedStore?.friendlyName ?? '';
  String get _pickUpAddressComplement => _addressFriendlyName.trim().isNotEmpty
      ? _addressFriendlyName.contains('Loja')
          ? _addressFriendlyName
          : 'Loja $_addressFriendlyName'
      : '';

  String get _deliveryDate {
    late final isOrderCanceled =
        TrackOrderUtils.getStatus(this) == OrderStatus.canceled;
    late final pickupDate = _selectedStore?.pickupAvailabilityWithDate;
    late final isPickupOrder =
        _selectedStore?.isPickupStore == true && pickupDate != null;
    late final deliveryDate = packageAttachment?.packages?.firstOrNull
            ?.courierStatus?.data?.firstOrNull?.lastChange ??
        '';
    late final isOrderDelivered =
        TrackOrderUtils.isDelivered(this) && deliveryDate.trim().isNotEmpty;

    if (isOrderCanceled) {
      return '';
    } else if (isPickupOrder) {
      return pickupDate!;
    } else if (isOrderDelivered) {
      return DateUtilsSM.deliveryDate(deliveryDate);
    } else if (shippingEstimateDate != null) {
      return DateUtilsSM.deliveryDate(shippingEstimateDate!);
    }
    return '';
  }

  String get _valueDiscount {
    final userOrderTotals = totals ?? [];
    final hasAnyDiscount = userOrderTotals.any((t) => t.id == "Discounts");
    final isDiscountOver =
        userOrderTotals.singleWhere((t) => t.id == "Discounts").value! != 0;

    if (userOrderTotals.isNotEmpty && hasAnyDiscount && isDiscountOver) {
      return CurrencyUtils.format(
        amount: userOrderTotals.singleWhere((t) => t.id == "Discounts").value! *
            (-1).toDouble(),
        dividedBy100: true,
      );
    }
    return '';
  }
}

extension on StoreAddress {
  String? get asString {
    String? join(List<String?> strs, String separator) {
      return strs
          .whereType<String>()
          .where((element) => element.isNotEmpty)
          .join(separator);
    }

    return join([
      join([street, number], ', '),
      complement,
      postalCode,
      neighborhood
    ], ' - ');
  }
}

class FidelityOrderDetails extends StatelessWidget {
  final String? orderId;
  final List<String>? packageIds;

  const FidelityOrderDetails({
    super.key,
    this.orderId = '',
    this.packageIds = const [],
  });

  @override
  Widget build(BuildContext context) {
    final fidelity = ModuleRegistry.root
        .mapPluginsOfType<MyOrderCheckingAccountByPurchasePlugin, Widget>(
            (p) => p.build(context, orderId, packageIds))
        .firstOrNull;

    return fidelity ?? const SizedBox.shrink();
  }
}

abstract class MyOrderCheckingAccountByPurchasePlugin implements Plugin {
  int get index;
  Widget build(BuildContext context, String? orderId, List<String>? packageIds);
}

abstract class OrderDetailsSectionPlugin extends Plugin {
  int get index;
  Widget? build(BuildContext context, ListUserOrder? listUserOrder,
      bool isReturned, bool canBeReturned);
}

class _DevolutionButton extends StatelessWidget {
  final String webviewRoute;
  final String devolutionRouteUrl;

  const _DevolutionButton(
      {required this.webviewRoute, required this.devolutionRouteUrl});

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return Padding(
      padding: EdgeInsets.only(
        top: tokens.spacingStack.md,
        bottom: tokens.spacingStack.md,
        right: tokens.spacingStack.xl,
        left: 60,
      ),
      child: SMButton.secondary(
        expanded: true,
        onPressed: () => navigateToNamed(
          webviewRoute,
          arguments: WebViewParams(
            url: devolutionRouteUrl,
            screenName: 'Devolução',
            allowJavascript: true,
            avoidKeyboard: false,
          ),
        ),
        child: Text(
          'Solicitar Devolução',
          style: tokens.typography.typeStyles.body.copyWith(
            color: tokens.colors.typography.pure2,
          ),
        ),
      ),
    );
  }
}

abstract class PickupTrackOrderDeliveryText implements Plugin {
  Widget build(OrderStatusData orderStatusData);
}

abstract class OrderStatusDeliveredPlugin implements Plugin {
  Widget build(ListUserOrder? order, bool? isDetailSection,
      SelectedDelivery? selectedDelivery);
}

abstract class BalanceFeedbackOrdeDatils extends Plugin {
  Widget? build(
    BuildContext context,
  );
}
