import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_core/modules/catalog/models/product/item.dart';
import 'package:soma_core/modules/checkout/controllers/checkout_controller.dart';
import 'package:soma_core/modules/checkout/models/index.dart';
import 'package:soma_ui/widgets/templates/checkout/orderReview/components/sm_bottom_sheet_purchase_now_item.dart';
import 'package:soma_ui/widgets/templates/pdp/full_look/sm_pdp_full_look_controller.dart';
import 'package:soma_ui/widgets/templates/wishlist/bottom_sheet/sm_full_look_products_bottom_sheet.dart';
import 'package:soma_ui/widgets/templates/wishlist/bottom_sheet/sm_product_card_bottom_sheet.dart';

import '../../warn_me/bottom_sheet/sm_bottom_sheet_warn_me.dart';

typedef OnTapTableSizes = void Function(Item? activeItem)?;

class SMBottomSheetAddToBag extends StatefulWidget {
  final Product item;
  final ProdutcOrderForm? itemCard;
  final Item? selectedSize;
  final bool openOfAnavailableProduct;
  final bool featureItem;
  final String screenClass;
  final bool showSizeButtons;
  final bool showProductCardInFullLook;
  final bool initFullLook;
  final bool purchaseNow;
  final bool showStoreAvailability;
  final bool cameFromWishlist;
  final String? itemListName;

  const SMBottomSheetAddToBag({
    super.key,
    required this.item,
    required this.screenClass,
    this.featureItem = false,
    this.itemCard,
    this.selectedSize,
    this.openOfAnavailableProduct = false,
    this.showSizeButtons = false,
    this.showProductCardInFullLook = false,
    this.initFullLook = false,
    this.purchaseNow = false,
    this.showStoreAvailability = false,
    this.cameFromWishlist = false,
    this.itemListName,
  });

  factory SMBottomSheetAddToBag.forProduct(
    Product product, {
    Item? selectedSize,
    bool featureItem = false,
    required String screenClass,
    bool initFullLook = false,
    bool purchaseNow = false,
    bool cameFromWishlist = false,
    String? itemListName,
  }) {
    return SMBottomSheetAddToBag(
      screenClass: screenClass,
      item: product,
      itemCard: ProdutcOrderForm.fromProduct(product),
      selectedSize: selectedSize,
      openOfAnavailableProduct: product.isAvailability,
      featureItem: featureItem,
      initFullLook: initFullLook,
      purchaseNow: purchaseNow,
      cameFromWishlist: cameFromWishlist,
      itemListName: itemListName,
    );
  }

  @override
  State<SMBottomSheetAddToBag> createState() => _SMBottomSheetAddToBagState();
}

class _SMBottomSheetAddToBagState extends State<SMBottomSheetAddToBag>
    with
        SingleTickerProviderStateMixin,
        AppRoutesStateMixin,
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  bool shouldSelectSize = true;
  bool isAddingToBag = false;
  late Item activeItem;
  late final FullLookController? fullLookController = _getFullLookController();
  late final isFullLookEnabled =
      appFeaturesConfig.fullLookConfig.showInAddToBagBottomSheet;

  @override
  void initState() {
    super.initState();

    if (isFullLookEnabled && widget.initFullLook) {
      scheduleMicrotask(() async {
        await fullLookController?.initFullLook(widget.item);
      });
    }

    setActiveItem();

    for (var i = 0; i < widget.item.items!.length; i++) {
      final item = widget.item.items![i];
      debugPrint(
          'SMBottomSheetAddToBag: _buildSizeSelector item:${i} ${item.itemSize}');
    }
  }

  @override
  void didUpdateWidget(covariant SMBottomSheetAddToBag oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.selectedSize?.itemId != oldWidget.selectedSize?.itemId) {
      setState(() {
        setActiveItem();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectedSize != null &&
        isAddingToBag &&
        widget.purchaseNow == false) {
      return _buildBottomSheet(
        buttonTitle: 'adicionando produto à ${termsAndMessages.bag}',
        title: textTransform
            .title('adicionando produto à ${termsAndMessages.bag}'),
        bodyHeight: 0,
        body: const SizedBox.shrink(),
      );
    }

    return SmObserver(
      builder: (context) {
        if (widget.showStoreAvailability) {
          return _buildBottomSheet(
            buttonTitle: textTransform.button('Verificar a disponibilidade'),
            hideButton: false,
            title: textTransform.title('Tamanho'),
            body: SelectedSize(
              items: widget.item.items!.toList(),
              isLoading: !itemsController.isLoading.value!,
              activeItem: activeItem,
            ),
            subtitle: termsAndMessages.availabilityCheckMessage ??
                textTransform.body(
                    'Selecione somente um tamanho para verificar a disponibilidade nas lojas'),
            bodyHeight: 105.0,
            alignment: Alignment.centerLeft,
          );
        }

        if (shouldSelectSize) {
          final showSizeButtons =
              pdpTheme.addToBagBottomSheetTheme?.sizeSelector.isSizeButtons ??
                  false;

          return _buildBottomSheet(
            title: textTransform.title(
              termsAndMessages.addToBagBottomSheetSizeSelectTitle ??
                  'selecione o seu tamanho',
            ),
            buttonTitle: activeItem.isAvailable
                ? 'adicionar à ${termsAndMessages.bag}'
                : termsAndMessages.productOutOfStock,
            hideButton: (activeItem.isAvailable || warnMeTheme.showWarnMe) &&
                showSizeButtons,
            body: showSizeButtons ? _buildSizeButtons() : _buildSizeSelector(),
            bodyHeight: showSizeButtons ? 65.0 : 250.0,
            alignment: Alignment.centerLeft,
          );
        }

        if (!shouldSelectSize &&
            widget.purchaseNow &&
            itemsController.isLoading.value != null) {
          return SMBottomSheetPurchaseNowItem(
            orderAddItem: OrderAddItem(
                id: activeItem.itemId!,
                quantity: 1,
                seller: activeItem.firstAvailableSeller?.sellerId ?? ''),
          );
        }

        final showProductCardWhenInWishlist = pdpTheme.addToBagBottomSheetTheme
            ?.sizeButtonsTheme?.redirectToProductCardWhenInWishlist;

        final showProductCardBottomSheet = !isFullLookEnabled ||
            (isFullLookEnabled &&
                showProductCardWhenInWishlist == true &&
                !hasOtherFullLookProducts &&
                widget.cameFromWishlist);

        return showProductCardBottomSheet
            ? SMProductCardBottomSheet(
                activeItem: activeItem,
                itemCard: widget.itemCard,
                product: widget.item,
                onTapAction: onTapAction,
                isLoading: isAddingToBag || itemsController.isLoading.value!,
                hasOtherLookProducts:
                    widget.item.otherLookProducts?.isNotEmpty == true,
              )
            : SMFullLookProductsBottomSheet(
                activeItem: activeItem,
                product: widget.item,
                onTapAction: onTapAction,
                isLoading: isAddingToBag || itemsController.isLoading.value!,
                fullLookItems: otherFullLookItems ?? [],
              );
      },
    );
  }

  FullLookController? _getFullLookController() {
    if (!isFullLookEnabled) return null;

    if (context.serviceLocator.canLocate<FullLookController>()) {
      final fullLookController =
          context.serviceLocator.locate<FullLookController>();

      return fullLookController;
    }
    return null;
  }

  List<FullLookItem>? get otherFullLookItems {
    if (fullLookController == null ||
        fullLookController!.hasCompleteFullLook.value == false) {
      return null;
    }

    return fullLookController!.fullLookItems
        .where((i) => i.productOrderForm.productId != widget.item.productId)
        .toList();
  }

  bool get hasOtherFullLookProducts =>
      otherFullLookItems != null && otherFullLookItems!.isNotEmpty;

  Widget _buildBottomSheet({
    required String buttonTitle,
    required String title,
    required Widget body,
    bool hideButton = false,
    bool hideHeader = false,
    double bodyHeight = 250,
    Alignment? alignment,
    String? subtitle,
  }) {
    return SMBottomSheet(
      onTap: () {
        onTapAction();
      },
      buttonTitle: textTransform.button(buttonTitle),
      hideButton: hideButton,
      hideHeader: hideHeader,
      title: textTransform.title(title),
      subtitle: subtitle,
      onTapClose: () => Navigator.pop(context),
      isLoading: isAddingToBag || itemsController.isLoading.value!,
      child: SizedBox(
        height: bodyHeight,
        child: Align(
          alignment: alignment ?? Alignment.center,
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: body,
          ),
        ),
      ),
    );
  }

  void _chooseSize() {
    shouldSelectSize = false;
  }

  void setActiveItem() {
    final addToBagCondition = widget.selectedSize != null &&
        widget.selectedSize!.isAvailable &&
        !widget.purchaseNow;

    if (addToBagCondition) {
      activeItem = widget.selectedSize!;
      addItemToBag(activeItem);
    } else if (widget.purchaseNow && widget.selectedSize != null) {
      activeItem = widget.selectedSize!;
      if (shouldSelectSize) {
        _chooseSize();
      }
    } else if (widget.item.isAvailability) {
      activeItem = widget.item.items!.firstWhere((item) => item.isAvailable);
    } else {
      activeItem = widget.item.items![0];
    }
  }

  Future<void> onTapAction() async {
    if (widget.showStoreAvailability == true) {
      navigateToNamed(routes.checkAvailibilityInStore!, arguments: activeItem);
    } else if (!activeItem.isAvailable) {
      warneMe();
    } else if (shouldSelectSize) {
      await analyticsController.logProductSizeSelected(widget.item.productId,
          widget.item.productName, activeItem.tamanho?.first);
      if (widget.purchaseNow) {
        addPurchaseNowItem(activeItem);
      } else {
        addItemToBag(activeItem);
      }
    } else {
      analyticsController.logSelectContent(
        contentType: 'compre-o-look:ver-sacola',
      );
      navigateToNamed(
        checkoutRoute,
        arguments: true,
        preventDuplicates: false,
      );
    }
  }

  void warneMe() {
    if (warnMeTheme.showWarnMe) {
      Navigator.pop(context);
      customShowBottomSheet(
        SMBottomSheetWarnMe(
          skuId: activeItem.itemId ?? '',
          title: textTransform.title(widget.openOfAnavailableProduct
              ? 'seu tamanho acabou ${termsAndMessages.sadSmile}'
              : 'ops, esse produto esgotou ${termsAndMessages.sadSmile}'),
        ),
      );
    } else {
      const SizedBox.shrink();
    }
  }

  Future<void> addItemToBag(Item item) async {
    try {
      setState(() {
        isAddingToBag = true;
      });

      await itemsController.addItem(orderAddItem: [
        OrderAddItem(
          id: item.itemId!,
          quantity: 1,
          seller: item.firstAvailableSeller?.sellerId ?? '',
        )
      ]);

      orderForm?.isFeatureItem = widget.featureItem;

      if (mounted) {
        setState(() {
          shouldSelectSize = false;
        });
      }
    } catch (error, stackTrace) {
      showSnackBar(SMSnackBarGetX(
        designTokens: tokens,
        textLabel: 'ocorreu um erro inesperado tente novamente mais tarde!',
        errorMessage: true,
        textColor: colors.typography.pure1,
      ));
      analyticsController.logError(
        error.toString(),
        stackTrace,
        fatal: true,
        orderForm: checkoutController.orderForm.value?.toJson(),
      );
    } finally {
      if (mounted) {
        setState(() {
          isAddingToBag = false;
        });
      }
    }

    try {
      dispatchSelectContentEvent('compra-rapida:adicionar–ao-carrinho');
      dispatchLogAddToCartEvent(
        product: widget.item,
        screenClass: widget.screenClass,
        item: item,
        featureItem: widget.featureItem,
        orderForm: orderForm,
        screenName: 'Bottom Sheet Adicionar ao Carrinho',
        itemListName: widget.itemListName,
      );
    } catch (e) {
      debugPrint('Error on get creativeSlot');
    }
  }

  Future<void> addPurchaseNowItem(Item item) async {
    setState(() {
      activeItem = item;
    });

    if (checkoutController.getCartItems() == 0) {
      await checkoutController.itemsController.addItem(
        orderAddItem: [
          OrderAddItem(
            id: item.itemId!,
            quantity: 1,
            seller: item.firstAvailableSeller?.sellerId ?? '',
          ),
        ],
      );

      return await _goToWhichRouteCheckout();
    }

    if (shouldSelectSize) {
      _chooseSize();
    }
  }

  Future<void> _goToWhichRouteCheckout() async {
    final routesUser = await authController.routesUserLoggedOrNot();

    switch (routesUser) {
      case RoutesUserLogged.payment:
        navigateToNamed(paymentMethodRoute);
        break;
      case RoutesUserLogged.orderReview:
        navigateToNamed(orderReviewRoute);
        break;

      case RoutesUserLogged.shippingRegistration:
        navigateToNamed(shippingRegistrationRoute);
        break;

      case RoutesUserLogged.addressRegistration:
        navigateToNamed(addressRegistrationRoute);
        break;

      case RoutesUserLogged.userRegistration:
        navigateToNamed(userRegistrationRoute);
        break;

      case RoutesUserLogged.login:
        ModuleRegistry.root
            .pluginsOfType<LoginPlugin>()
            .firstOrNull
            ?.execute(LoginFlow.checkout);
        break;
      default:
        return;
    }
  }

  bool get _hasUnavailableSizes {
    return widget.item.items!.any((i) => !i.isAvailable);
  }

  Widget _buildSizeSelector() {
    final data = widget.item.items!.asMap().entries.map((entry) {
      int idx = entry.key;
      Item val = entry.value;

      String? textSuport;
      if (val.hasOne) {
        textSuport = termsAndMessages.lastProductMessage;
      }
      if (val.hasTwo) {
        textSuport = 'restam apenas 2!';
      }
      if (!val.isAvailable) {
        textSuport = termsAndMessages.productOutOfStock;
      }

      final size = val.tamanho?.elementAtOrNull(idx) ??
          val.tamanho?.firstOrNull ??
          'indefinido';

      return PickerData(label: size, textSuport: textSuport);
    }).toList();
    final scrollController = FixedExtentScrollController(
        initialItem: widget.item.items?.indexOf(activeItem) ?? 0);

    return Column(
      children: [
        Row(
          children: [
            if (pdpTheme.belowSelectSizeActions
                    .contains(BelowSelectSizeActions.measurements) &&
                (pdpTheme.addToBagBottomSheetTheme?.sizePickerTheme
                        ?.hasTableSizeAndMySizeIsOverButtonsInAddToBagBottomSheet ??
                    false)) ...[
              SMButton.link(
                onPressed: onTapTableSizes,
                trailing: Icon(icons.arrowRight, size: 10),
                child: Text(
                  textTransform
                      .body(tokens.termsAndMessages.sizesTableBottomSheetTitle),
                  style: typography.typeStyles.bodySm,
                ),
              ),
              SizedBox(width: spacingInline.sm),
            ],
            if (pdpTheme.belowSelectSizeActions
                    .contains(BelowSelectSizeActions.outOfStock) &&
                _hasUnavailableSizes &&
                (pdpTheme.addToBagBottomSheetTheme?.sizePickerTheme
                        ?.hasTableSizeAndMySizeIsOverButtonsInAddToBagBottomSheet ??
                    false))
              SMButton.link(
                onPressed: onTapMySizeIsOver,
                trailing: Icon(icons.arrowRight, size: 10),
                child: Text(
                  textTransform.body('meu tamanho acabou'),
                  style: typography.typeStyles.bodySm,
                ),
              )
          ],
        ),
        SizedBox(
          height: pdpTheme.addToBagBottomSheetTheme?.sizePickerTheme
                      ?.hasTableSizeAndMySizeIsOverButtonsInAddToBagBottomSheet ??
                  false
              ? 210.0
              : 250.0,
          child: !itemsController.isLoading.value!
              ? SMPicker(
                  initialItem: widget.item.items?.indexOf(activeItem),
                  scrollController: scrollController,
                  onInitCallback: () {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (config.intelligentSearchConfig.sizeOrder != null) {
                        final sortedItems = sortItems(
                            data, config.intelligentSearchConfig.sizeOrder!);

                        final initialLabel =
                            data[widget.item.items?.indexOf(activeItem) ?? 0]
                                .label;

                        final index = sortedItems
                            .indexWhere((item) => item.label == initialLabel);

                        scrollController.animateToItem(
                          index,
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeOut,
                        );
                      }
                    });
                  },
                  data: data,
                  onSelectedItemChanged: (index) {},
                  onSelectedItemChangedValue: (value) {
                    final index = widget.item.items!
                        .indexWhere((item) => item.itemSize == value);
                    setState(() {
                      activeItem = widget.item.items![index];
                    });
                  },
                )
              : Container(),
        )
      ],
    );
  }

  List<PickerData> sortItems(
    List<PickerData> items,
    List<String> defaultBrandSizes,
  ) {
    final matchingItems =
        items.where((item) => defaultBrandSizes.contains(item.label)).toList();
    matchingItems.sort((a, b) => defaultBrandSizes
        .indexOf(a.label)
        .compareTo(defaultBrandSizes.indexOf(b.label)));

    final nonMatchingItems = items
        .where((item) => defaultBrandSizes.contains(item.label) == false)
        .toList();
    nonMatchingItems.sort((a, b) => a.label.compareTo(b.label));

    final sortedItems = [...nonMatchingItems, ...matchingItems];
    final indexRn = sortedItems.indexWhere((element) => element.label == "RN");

    if (indexRn != -1) {
      final filterRN = sortedItems[indexRn];
      sortedItems.removeAt(indexRn);
      sortedItems.insert(0, filterRN);
    }

    return sortedItems;
  }

  Widget _buildSizeButtons() {
    return Column(
      children: [
        SizedBox(
          child: itemsController.isLoading.value! == false
              ? Wrap(
                  spacing: tokens.spacingStack.xxs,
                  children: widget.item.items!.map((item) {
                    String? textSupport;

                    if (item.hasOne) {
                      textSupport = "Só tem 1!";
                    }

                    if (item.hasTwo) {
                      textSupport = "Só tem 2!";
                    }

                    return SMSizeButton(
                      text: TextUtils.verifySizeText(item.tamanho?.first ?? ''),
                      isDisabled: !item.isAvailable,
                      description: textSupport,
                      hasDescription: true,
                      descriptionStyle: TextStyle(
                        fontSize: tokens.typography.fontSizes.xxus,
                        fontFamily: tokens.typography.fontFamilyPrimary,
                        color: tokens.colors.feedback.pureError,
                      ),
                      textStyle: tokens.typography.typeStyles.buttonSm,
                      textColor: !item.isAvailable
                          ? tokens.colors.typography.dark2
                          : tokens.colors.typography.pure1,
                      textVariant: TextVariant.upperCase,
                      onTap: () {
                        setState(() {
                          activeItem = item;
                        });
                        onTapAction();
                      },
                    );
                  }).toList(),
                )
              : Container(),
        )
      ],
    );
  }

  void onTapTableSizes() {
    customShowBottomSheet(
      SMBottomSheetSizeTable(
        product: widget.item,
        selectedSize: widget.selectedSize?.tamanho![0],
      ),
    );
  }

  void onTapMySizeIsOver() async {
    _selectSizeForWarnMe().then((selectedSizeSku) {
      if (selectedSizeSku != null) {
        Navigator.pop(context);
        _showWarnMeModal(selectedSizeSku);
      }
    });
  }

  Future<String?> _selectSizeForWarnMe() {
    final completer = Completer<String?>();
    customShowBottomSheet(
      SMBottomSheetChooseSize(
        sizes: widget.item.items!.where((s) => !s.isAvailable).toList(),
        onSizeSelected: (size) => completer.complete(size.itemId),
        isButtonDisabledFn: (item) => item == null,
        hasSupportText: false,
      ),
    )
        .then((_) => completer.isCompleted ? null : completer.complete(null))
        .catchError(
            (e) => completer.isCompleted ? null : completer.completeError(e));
    return completer.future;
  }

  void _showWarnMeModal(String? skuId) {
    customShowBottomSheet(
      SMBottomSheetWarnMe(skuId: skuId ?? ''),
      enableDrag: false,
    );
  }
}

ProdutcOrderForm? getBagProduct({
  required CheckoutController checkoutController,
  required Item activeItem,
  ProdutcOrderForm? itemCard,
  DesignTokens? tokens,
}) {
  final bagProductItems = checkoutController.orderForm.value?.items;
  final bagProduct = bagProductItems?.firstWhereOrNull(
      (item) => item.isGift == false && item.id == activeItem.itemId);
  final product = bagProduct?.copyWith(
    name: tokens?.textTransform.body(
        activeItem.complementName ?? itemCard?.name ?? bagProduct.name ?? ''),
    size: activeItem.itemSize,
    price: (activeItem.firstAvailableSeller?.commertialOffer?.listPrice ?? 0) *
        100,
  );
  return product;
}

class SelectedSize extends StatefulWidget {
  final List<Item> items;
  final bool isLoading;
  final Item? activeItem;

  const SelectedSize({
    super.key,
    required this.items,
    required this.isLoading,
    this.activeItem,
  });

  @override
  _SelectedSizeState createState() => _SelectedSizeState();
}

class _SelectedSizeState extends State<SelectedSize> {
  late Item? activeItem;

  @override
  void initState() {
    super.initState();
    activeItem = widget.activeItem;
  }

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    if (widget.isLoading) return const SizedBox.shrink();
    return Column(
      children: [
        SizedBox(
          child: Wrap(
            spacing: tokens.spacingStack.xxs,
            children: widget.items.map((item) {
              return SMSizeButton(
                text: TextUtils.verifySizeText(item.tamanho?.first ?? ''),
                isDisabled: !item.isAvailable,
                hasDescription: true,
                descriptionStyle: TextStyle(
                  fontSize: tokens.typography.fontSizes.xxus,
                  fontFamily: tokens.typography.fontFamilyPrimary,
                  color: tokens.colors.feedback.pureError,
                ),
                textVariant: TextVariant.upperCase,
                onTap: () {
                  setState(() {
                    activeItem = item;
                  });
                },
                isActive: activeItem?.itemSize == item.itemSize,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
