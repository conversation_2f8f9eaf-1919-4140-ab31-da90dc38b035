import 'dart:math';

import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';

typedef ProductsGridLayoutItemBuilder = Widget Function(
  BuildContext context,
  int index,
  bool isSpotlight,
);

class SliverProductsGridLayout extends StatefulWidget {
  const SliverProductsGridLayout({
    super.key,
    required this.crossAxisCount,
    required this.itemCount,
    required this.itemBuilder,
    this.padding,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.rowsBetweenSpotlights = 3,
    this.mediaKitEntireRow,
  });

  final EdgeInsets? padding;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final int crossAxisCount;
  final int itemCount;
  final int? rowsBetweenSpotlights;
  final ProductsGridLayoutItemBuilder itemBuilder;
  final int? mediaKitEntireRow;

  @override
  State<SliverProductsGridLayout> createState() =>
      _SliverProductsGridLayoutState();
}

class _SliverProductsGridLayoutState extends State<SliverProductsGridLayout> {
  late var rowStartingIndexCalculationStrategy =
      DifferentSizeRowStartingIndexCalculationStrategy(
    standardRowSize: widget.crossAxisCount,
  );

  @override
  void didUpdateWidget(covariant SliverProductsGridLayout oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.crossAxisCount != widget.crossAxisCount ||
        oldWidget.rowsBetweenSpotlights != widget.rowsBetweenSpotlights) {
      rowStartingIndexCalculationStrategy =
          DifferentSizeRowStartingIndexCalculationStrategy(
        standardRowSize: widget.crossAxisCount,
      );
    }
  }

  int getSpotlightRows() {
    int totalRows = (widget.itemCount ~/ 2);
    int spotlightRows = (totalRows ~/ (widget.rowsBetweenSpotlights ?? 1));
    return spotlightRows;
  }

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: widget.padding ?? EdgeInsets.zero,
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          childCount: widget.itemCount +
              getSpotlightRows() +
              (widget.mediaKitEntireRow ?? 0),
          (context, index) {
            if (widget.crossAxisCount == 1) {
              return _buildRow(context, index);
            }

            if (index.isEven) {
              final rowIndex = index ~/ 2;
              return _buildRow(context, rowIndex);
            }

            return SizedBox(height: widget.mainAxisSpacing);
          },
        ),
      ),
    );
  }

  Widget? _buildRow(BuildContext context, int index) {
    final rowsBetweenSpotlights = widget.rowsBetweenSpotlights;
    final isSpotlightRow = rowsBetweenSpotlights != null &&
        (index + 1) % (rowsBetweenSpotlights + 1) == 0;
    final rowCrossAxisCount = isSpotlightRow ? 1 : widget.crossAxisCount;
    final items = _getItemsForRow(
      context,
      index,
      crossAxisCount: rowCrossAxisCount,
      isSpotlight: isSpotlightRow,
    );

    if (items.isEmpty) return null;

    return _GridRow(
      rowIndex: index,
      items: items,
      crossAxisSpacing: widget.crossAxisSpacing ?? 0,
    );
  }

  List<Widget> _getItemsForRow(
    BuildContext context,
    int rowIndex, {
    required int crossAxisCount,
    required bool isSpotlight,
  }) {
    final content = <Widget>[];
    var occupiedCrossAxisSlots = 0;
    var itemIndex =
        rowStartingIndexCalculationStrategy.getStartingIndexForRow(rowIndex);
    while (occupiedCrossAxisSlots < crossAxisCount &&
        itemIndex < widget.itemCount) {
      final item = widget.itemBuilder(context, itemIndex, isSpotlight);
      final crossAxisSize = item is ProductsGridTile ? item.crossAxisSize : 1;
      final remainingCrossAxisSlots = crossAxisCount - occupiedCrossAxisSlots;
      occupiedCrossAxisSlots += min(crossAxisSize, remainingCrossAxisSlots);
      content.add(item);
      itemIndex++;
    }
    rowStartingIndexCalculationStrategy.registerRowSize(
      row: rowIndex,
      size: content.length,
    );

    content.addAll(
      List.filled(
        crossAxisCount - occupiedCrossAxisSlots,
        const SizedBox.shrink(),
      ),
    );

    return content;
  }
}

class ProductsGridTile extends StatelessWidget {
  const ProductsGridTile({
    super.key,
    required this.crossAxisSize,
    required this.child,
  });

  final int crossAxisSize;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return child;
  }
}

class _GridRow extends StatelessWidget {
  const _GridRow({
    required this.items,
    required this.crossAxisSpacing,
    required this.rowIndex,
  });

  final List<Widget> items;
  final double crossAxisSpacing;
  final int rowIndex;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int index = 0; index < items.length; index++) ...[
          GridRowInheritedWidget(
            isFirstItem: (rowIndex == 0 && index == 0),
            child: Expanded(child: items[index]),
          )
        ]
      ].separated(SizedBox(width: crossAxisSpacing)),
    );
  }
}

class GridRowInheritedWidget extends InheritedWidget {
  final bool isFirstItem;

  const GridRowInheritedWidget({
    super.key,
    required this.isFirstItem,
    required super.child,
  });

  static GridRowInheritedWidget? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<GridRowInheritedWidget>();
  }

  @override
  bool updateShouldNotify(GridRowInheritedWidget oldWidget) {
    return isFirstItem != oldWidget.isFirstItem;
  }
}
