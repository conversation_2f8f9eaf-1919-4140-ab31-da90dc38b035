import 'dart:async';

import 'package:flutter/material.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

class SMOrders extends StatefulWidget {
  const SMOrders({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _SMOrdersState();
}

class _SMOrdersState extends State<SMOrders>
    with
        DesignTokensStateMixin,
        AppAssetsStateMixin,
        SomaCoreStateMixin,
        AppRoutesStateMixin,
        AnalyticsEventDispatcherStateMixin,
        OrdersScreenMixin {
  late ScrollController _scrollController;

  var argData = navigationArguments;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    dispatchLogScreenViewEvent(
      screenClass: 'Orders',
      screenName: 'Pedidos',
    );
    _scrollController.addListener(() {
      // nextPageTrigger will have a value equivalent to 80% of the list size.
      var nextPageTrigger = 0.8 * _scrollController.position.maxScrollExtent;
      // _scrollController fetches the next paginated data when the current postion of the user on the screen has surpassed
      if (_scrollController.position.pixels > nextPageTrigger && !isLastPage) {
        setState(() {
          isLoading = true;
        });
        getUserOrders();
        getUserOrdersReturnsResume();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final completeAndActiveOrdersAreEmpty =
        userActiveOrdersList.isEmpty && userCompleteOrdersList.isEmpty;
    final shouldBuildEmptyState =
        isLoading == false && completeAndActiveOrdersAreEmpty;

    return Scaffold(
      backgroundColor: colors.neutral.pure1,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton:
          isLoading == false && completeAndActiveOrdersAreEmpty
              ? Padding(
                  padding: EdgeInsets.symmetric(horizontal: spacingInline.lg),
                  child: SMButton.primary(
                    expanded: true,
                    onPressed: () {
                      dispatchSelectContentEvent(
                          'conta:pedidos-e-devolucoes:explorar-novidades');
                      navigateToReplacementNamed(
                        searchResultRoute,
                        arguments: config.buttonsRedirects.emptyBag,
                      );
                    },
                    child: Text(
                      textTransform.button(
                        termsAndMessages.emptyOrdersButtonText ??
                            'explorar novidades',
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
      body: PopScope(
        canPop: canPop,
        onPopInvoked: (_) => onWillPop(),
        child: shouldBuildEmptyState ? buildEmptyState() : buildOrders(),
      ),
    );
  }

  Widget buildLoadingSkeleton() {
    return Column(
      children: [
        const SMNavBarLoadingSkeleton(),
        SizedBox(height: spacingStack.sm),
        Expanded(
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
            itemCount: 10,
            itemBuilder: (_, __) => const SMOrderItemLoadingSkeleton(),
            separatorBuilder: (_, __) => SizedBox(height: spacingStack.md),
          ),
        ),
      ],
    );
  }

  Widget buildEmptyState() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SMNavBar.adaptative(),
          SizedBox(height: spacingStack.lg),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: spacingInline.md),
            child: SMMainContent(
              title: textTransform.title('nenhum pedido realizado'),
              subtitle: textTransform.body(
                termsAndMessages.emptyOrdersSubtitle ??
                    'confira os produtos salvos na sua lista de desejos ou explore nossas novidades!',
              ),
              image: noOrdersImage,
              imageSize: 150.0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersSectionTitle(String title) {
    if (tokens.orderStatusTheme.showActiveOrdersLabels == false) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(
          top: spacingStack.lg,
          left: spacingInline.sm,
          right: spacingInline.sm),
      child: Text(
        textTransform.body(title),
        style: typography.typeStyles.subtitle,
      ),
    );
  }

  Widget buildOrders() {
    return CustomScrollView(
      controller: _scrollController,
      slivers: <Widget>[
        SmSliverAppBar(
          child: SMNavBar.adaptative(
            useSafeArea: false,
            title: Text(
              textTransform.headerTitle(termsAndMessages.ordersScreenTitle),
            ),
            leading: SmBackButton(
              onTap: onNavigateBack,
            ),
          ),
        ),
        if (userActiveOrdersList.isNotEmpty)
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                if (index == 0) {
                  return _buildOrdersSectionTitle('pedidos ativos');
                }

                final order = userActiveOrdersList[index - 1];
                return Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: spacingStack.md,
                        horizontal: spacingInline.sm),
                    child: SMOrderItem(
                      listUserOrder: order,
                      hasRequestedReturn: hasRequestedReturn(order),
                      isAvailableToReturn: (isAvailableToReturn(order) &&
                          !isReturnWindowClosed(order)),
                    ));
              },
              childCount: userActiveOrdersList.length + 1,
            ),
          ),
        if (userCompleteOrdersList.isNotEmpty)
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                if (index == 0) {
                  return _buildOrdersSectionTitle('pedidos concluídos');
                }
                final order = userCompleteOrdersList[index - 1];
                return Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: spacingStack.md,
                        horizontal: spacingInline.sm),
                    child: SMOrderItem(
                      listUserOrder: order,
                      hasRequestedReturn: hasRequestedReturn(order),
                      isAvailableToReturn: (isAvailableToReturn(order) &&
                          !isReturnWindowClosed(order)),
                    ));
              },
              childCount: userCompleteOrdersList.length + 1,
            ),
          ),
        if (isLoading)
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                return Padding(
                    padding: EdgeInsets.only(
                        top: spacingInset.sm,
                        left: spacingInline.sm,
                        right: spacingInline.sm),
                    child: const SMOrderItemLoadingSkeleton());
              },
              childCount: 10,
            ),
          ),
        if (isLastPage)
          SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom,
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text('Não há mais pedidos'),
                ],
              ),
            );
          }, childCount: 1)),
      ],
    );
  }
}

mixin OrdersScreenMixin<T extends StatefulWidget> on State<T> {
  AuthController get authController;
  bool isLoading = false;
  late bool isLastPage;
  late int pageNumber;
  final int numberOfOrdersPerRequest = 10;
  List<ListUserOrder> userActiveOrdersList = [];
  List<ListUserOrder> userCompleteOrdersList = [];
  List<String> userOrderReturnsOngoingIds = [];
  List<String> userOrderReturnsAvailables = [];
  late final String routeGoBack;

  @override
  void initState() {
    super.initState();

    final argData = navigationArguments;
    routeGoBack = (argData != null) ? argData[0]['routeGoBack'] ?? '' : '';
    pageNumber = 1;
    userActiveOrdersList = [];
    userCompleteOrdersList = [];
    isLastPage = false;
    isLoading = true;
    scheduleMicrotask(getUserOrders);
    scheduleMicrotask(getUserOrdersReturnsResume);
  }

  void getUserOrders() async {
    try {
      final userOrders = await authController.getUserOrders(
        pageNumber: pageNumber,
        pageSize: numberOfOrdersPerRequest,
      );

      if (mounted && pageNumber == (userOrders.pagination?.currentPage ?? 1)) {
        setState(() {
          isLoading = false;
          userActiveOrdersList.addAll(userOrders.list
                  ?.where((element) => (element.isCompleted ?? false) == false)
                  .toList() ??
              []);
          userCompleteOrdersList.addAll(userOrders.list
                  ?.where((element) => element.isCompleted ?? false)
                  .toList() ??
              []);
          isLastPage = pageNumber == (userOrders.pagination?.pages ?? 1);
          pageNumber = pageNumber + 1;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void getUserOrdersReturnsResume() async {
    Map<String, List<String>>? data;

    try {
      data = await ModuleRegistry.root
          .pluginsOfType<OrderReturnsResumePlugin>()
          .firstOrNull
          ?.execute();
    } catch (e) {
      debugPrint(e.toString());
    }

    if (mounted) {
      setState(() {
        userOrderReturnsAvailables = data?['available'] ?? [];
        userOrderReturnsOngoingIds = data?['ongoing'] ?? [];
      });
    }
  }

  bool hasRequestedReturn(ListUserOrder order) =>
      userOrderReturnsOngoingIds.any((element) => element == order.orderId);

  bool get canPop => routeGoBack.isEmpty;

  Future<void> onWillPop() async {
    if (routeGoBack.isNotEmpty) {
      await navigateToReplacementNamedAndRemoveUntil(
        routeGoBack,
        ModalRoute.withName(routeGoBack),
        null,
      );
    }
  }

  Future<void> onNavigateBack() async {
    if (routeGoBack.isNotEmpty) {
      await navigateToReplacementNamedAndRemoveUntil(
        routeGoBack,
        ModalRoute.withName(routeGoBack),
        null,
      );
    } else {
      navigateBack();
    }
  }

  bool isAvailableToReturn(ListUserOrder order) =>
      userOrderReturnsAvailables.any((element) => element == order.orderId);

  bool isReturnWindowClosed(ListUserOrder order) {
    if (order.cancellationDate != null) return true;

    if (TrackOrderUtils.isDelivered(order) == false) return false;

    final deliveredDate = DateTime.parse(order.packageAttachment?.packages
            ?.firstOrNull?.courierStatus?.data?.firstOrNull?.lastChange ??
        '');

    return DateTime.now().difference(deliveredDate).inDays > 15;
  }
}

abstract class OrderReturnsResumePlugin extends Plugin {
  Future<Map<String, List<String>>> execute();
}
