import 'package:flutter/material.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:diacritic/diacritic.dart';

class SMBottomSheetSizeTable extends StatefulWidget {
  final Product product;
  final String? selectedSize;
  final TableMeasures? measures;

  const SMBottomSheetSizeTable({
    super.key,
    required this.product,
    this.measures,
    this.selectedSize,
  });

  @override
  State<SMBottomSheetSizeTable> createState() => _SMBottomSheetSizeTableState();
}

class _SMBottomSheetSizeTableState extends State<SMBottomSheetSizeTable>
    with DesignTokensStateMixin {
  @override
  Widget build(BuildContext context) {
    final sizesTable = sizesTableTheme.sizesTable.forProduct(widget.product);
    final sizesize = sizesTableTheme.sizesTable.forProductAsync(widget.product);
    // Função para converter BodyMeasure para Map
    Map<String, dynamic> bodyMeasureToMap(BodyMeasure bodyMeasure) {
      return {
        'id': bodyMeasure.id,
        'name': bodyMeasure.name,
        'nameOnSizesTable': bodyMeasure.nameOnSizesTable,
        'measuringInstructions': bodyMeasure.measuringInstructions,
      };
    }

    // Função para converter SizeMeasures para Map
    Map<String, dynamic> sizeMeasuresToMap(SizeMeasures sizeMeasures) {
      return {
        'size': sizeMeasures.size,
        'measures': sizeMeasures.measures,
      };
    }

    // Função para converter SizesTable para Map
    Map<String, dynamic> sizesTableToMap(SizesTable sizesTable) {
      return {
        'type': sizesTable.type.toString(),
        'bodyMeasures': sizesTable.bodyMeasures.map(bodyMeasureToMap).toList(),
        'sizeMeasures': sizesTable.sizeMeasures.map(sizeMeasuresToMap).toList(),
        'measuringInstructions':
            sizesTable.measuringInstructions?.map(bodyMeasureToMap).toList(),
      };
    }

    // Converter para JSON e imprimir
    final sizesTableJson = sizesTableToMap(sizesTable);
    print(
        'SizesTable JSON: ${const JsonEncoder.withIndent('  ').convert(sizesTableJson)}');

    // Para o Future, você precisa aguardar o resultado
    sizesize?.then((asyncSizesTable) {
      final asyncSizesTableJson = sizesTableToMap(asyncSizesTable);
      print(
          'Async SizesTable JSON: ${const JsonEncoder.withIndent('  ').convert(asyncSizesTableJson)}');
    }).catchError((error) {
      print('Erro no async SizesTable: $error');
    });
    final title = textTransform.title(
      sizesTable.type == SizesTableType.jewelry
          ? 'Guia de tamanho'
          : termsAndMessages.sizesTableBottomSheetTitle,
      capitalizeFirstOnly: false,
    );
    final subtitle = termsAndMessages.sizesTableBottomSheetSubtitle != null
        ? textTransform
            .body(termsAndMessages.sizesTableBottomSheetSubtitle!.interpolate({
            'nameOfProduct': widget.product.productName ?? '',
          }))
        : null;

    final brandName = removeDiacritics(widget.product.brand ?? '')
        .toLowerCase()
        .replaceAll(' ', '_');

    ImageProvider? brandImage;
    if (brandName != '' && sizesTableTheme.brandImages != null) {
      brandImage = sizesTableTheme.brandImages![brandName];
    }

    return (bottomSheetSizeTableTheme.hasEmptyBottomSheetSizeTable &&
            sizesTable.sizeMeasures.isEmpty &&
            sizesTable.bodyMeasures.isEmpty &&
            widget.measures == null)
        ? SMBottomSheet(
            title: termsAndMessages.emptyBottomSheetSizeTableTitle,
            subtitle: termsAndMessages.emptyBottomSheetSizeTableSubtitle,
            onTapClose: () => Navigator.pop(context),
            onTap: () => Navigator.pop(context),
            hideButton: false,
            buttonTitle: termsAndMessages.emptyBottomSheetSizeTableButtonText,
            buttonTextColor: colors.typography.pure1,
            buttonBackgroundColor: colors.neutral.pure2,
            child: _buildEmptyContent(),
          )
        : SMBottomSheet(
            title:
                bottomSheetSizeTableTheme.hasScrollableHeader ? title : title,
            // titleStyle: typography.typeStyles.buttonSm,
            subtitle:
                bottomSheetSizeTableTheme.hasScrollableHeader ? null : subtitle,
            childHeightFactor:
                bottomSheetSizeTableTheme.bottomSheetHeightFactor,
            padding: bottomSheetSizeTableTheme.padding,
            bottom: bottomSheetSizeTableTheme.bottom,
            onTapClose: () => Navigator.pop(context),
            hideButton: true,
            showDragHandling: true,
            hideHeader: false,
            // image: brandImage,
            child: _buildContent(
              bottomSheetSizeTableTheme,
              measures: widget.measures,
              title: title,
              subtitle: subtitle,
              brandImage: brandImage,
              // title:
              //     sizesTableTheme.sizesTableBottomSheetHeaderSubtitle ?? title,
            ),
            //   SMBottomSheet(
            //   title: bottomSheetSizeTableTheme.hasScrollableHeader ? null : title,
            //   subtitle:
            //       bottomSheetSizeTableTheme.hasScrollableHeader ? null : subtitle,
            //   childHeightFactor:
            //       bottomSheetSizeTableTheme.bottomSheetHeightFactor,
            //   padding: bottomSheetSizeTableTheme.padding,
            //   bottom: bottomSheetSizeTableTheme.bottom,
            //   onTapClose: () => Navigator.pop(context),
            //   hideButton: true,
            //   child: _buildContent(
            //     bottomSheetSizeTableTheme,
            //     measures: widget.measures,
            //     title: title,
            //     subtitle: subtitle,

            //   ),
          );
  }

  Widget _buildEmptyContent() {
    return const SizedBox();
  }

  Widget _buildContent(
    BottomSheetSizeTableTheme theme, {
    TableMeasures? measures,
    required String title,
    required String? subtitle,
    ImageProvider? brandImage,
  }) {
    final sizesTable = sizesTableTheme.sizesTable
        .forProduct(widget.product, tableMeasures: widget.measures);

    final hideTopInfo = sizesTable.type == SizesTableType.jewelry &&
        tokens.sizesTableTheme.hideTitleWhenJewelryImage;

    return NestedScrollView(
      headerSliverBuilder: (_, __) => [
        SliverToBoxAdapter(
          child: Column(
            children: [
              if (theme.hasScrollableHeader && !hideTopInfo) ...[
                Padding(
                  padding: theme.headerPadding ?? const EdgeInsets.all(0),
                  child: SMMainContent(
                    title: title,
                    image: brandImage,
                    // titleStyle:
                    //     sizesTableTheme.sizesTableBottomSheetHeaderSubtitle !=
                    //             null
                    //         ? typography.typeStyles.subtitle
                    //         : null,
                    subtitle: subtitle,
                    // subtitleStyle:
                    //     sizesTableTheme.sizesTableBottomSheetHeaderSubtitle !=
                    //             null
                    //         ? typography.typeStyles.bodyXs?.copyWith(
                    //             color: tokens.colors.typography.light2,
                    //           )
                    //         : null,
                  ),
                ),
              ],
              if (theme.hasHeaderDivider && !hideTopInfo)
                SMLine(
                  lineSize: LineSize.small,
                  lineColor: colors.neutral.light2,
                ),
            ],
          ),
        ),
      ],
      body: SMSizesTable(
        product: widget.product,
        selectedSize: widget.selectedSize,
        padding: theme.hasHeaderDivider
            ? EdgeInsets.only(top: spacingStack.lg)
            : null,
        measures: measures,
      ),
    );
  }
}
