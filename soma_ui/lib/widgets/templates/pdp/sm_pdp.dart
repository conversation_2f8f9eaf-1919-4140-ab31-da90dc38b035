import 'dart:async';

import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_ui/cms/models/components/pdp_stamp_item_cms_component.dart';
import 'package:soma_core/modules/catalog/controllers/catalog_controller.dart';
import 'package:soma_core/modules/catalog/models/product/item.dart';
import 'package:soma_ui/widgets/templates/pdp/widgets/sm_pdp_scaffold.dart';

class SMPdp extends StatefulWidget {
  final Product product;
  final bool? featureItem;

  const SMPdp({
    super.key,
    required this.product,
    this.featureItem = false,
  });

  @override
  State<SMPdp> createState() => SMPdpState();
}

class SMPdpState extends State<SMPdp>
    with
        AppAssetsStateMixin,
        AnalyticsEventDispatcherStateMixin,
        DesignTokensStateMixin,
        SomaCoreStateMixin {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      dispatchLogScreenViewEvent(
        screenClass: 'Pdp',
        screenName: 'Detalhes do produto',
        firebaseScreenName: textTransform.screenNameEvent(
          widget.product.productName,
        ),
      );

      dispatchlogViewItemEvent(
        product: widget.product,
        screenClass: 'Pdp',
        screenName: 'Detalhes do produto',
        featureItem: widget.featureItem ?? false,
      );
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PDPProvider(
      notifier: PDPNotifier(
        PDPModel(
          product: widget.product,
          config: config,
          favoriteAnimationMain: favoriteAnimation,
          favoriteAnimationDetail: favoriteExplosionAnimation,
        ),
      ),
      child: AnalyticsMetadataProvider(
        metadata: const {
          'screen_name': 'Detalhes Do Produto',
          'screen_class': 'SMPdp',
        },
        child: const PDPScaffold(),
      ),
    );
  }
}

class PdpCmsStampTile extends StatelessWidget {
  const PdpCmsStampTile({
    super.key,
    required this.url,
    required this.title,
    required this.description,
  });

  final String url;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 56,
          height: 56,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: Color(0xFFF7F7F7),
            shape: BoxShape.circle,
          ),
          child: SMImage(
            image: NetworkImage(url),
            imageHeight: 27,
            imageWidth: 27,
          ),
        ),
        SizedBox(width: context.designTokens.spacingStack.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: context.designTokens.typography.typeStyles.bodyMd,
              ),
              SizedBox(height: context.designTokens.spacingStack.xxs),
              Text(
                description,
                style: context.designTokens.typography.typeStyles.bodyCaption
                    .copyWith(
                  color: context.designTokens.colors.typography.light2,
                  fontSize: 12,
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}

class PdpCmsStampChip extends StatelessWidget {
  const PdpCmsStampChip({
    super.key,
    required this.url,
    required this.title,
  });

  final String url;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.designTokens.spacingStack.sm),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF).withOpacity(0.6),
        borderRadius:
            context.designTokens.productsGridTheme.plusButtonBorderRadius,
      ),
      child: Row(
        children: [
          SMImage(
            image: NetworkImage(url),
            imageHeight: 20,
            imageWidth: 20,
          ),
          SizedBox(width: context.designTokens.spacingStack.sm),
          Text(title,
              style: context.designTokens.typography.typeStyles.bodySm.copyWith(
                decoration: TextDecoration.underline,
              ))
        ],
      ),
    );
  }
}

abstract class PdpFixedHeaderPlugin implements Plugin {
  Widget? build(BuildContext context, Product product);
}

class PDPModel {
  final Config config;
  final Product product;
  final bool? featureItem;

  final Item? selectedSize;
  final bool isGiftCard;
  final bool showBottomInfo;
  final bool hasMessage;
  final bool canScroll;
  final int activeItemIndex;
  final String? favoriteAnimationMain;
  final String? favoriteAnimationDetail;
  final CmsProductPdpDocument? pdpDocument;
  final bool isOnComboStepsMode;
  final GlobalKey? comboStepsKey;

  PDPModel({
    required this.product,
    required this.config,
    this.featureItem,
    this.selectedSize,
    this.favoriteAnimationMain,
    this.favoriteAnimationDetail,
    this.isGiftCard = false,
    this.canScroll = true,
    this.showBottomInfo = false,
    this.hasMessage = false,
    this.activeItemIndex = 0,
    this.pdpDocument,
    this.isOnComboStepsMode = false,
    this.comboStepsKey,
  }) {
    final i =
        product.imageSku.isNotEmpty ? product.imageSku : product.coverImage;

    debugPrint(
      'PDPModel created with ID: ${product.productId}, SIZE: ${selectedSize?.itemSize} and COLOR: ${i}',
    );
  }

  bool get productExists => product.productId.isNotNullOrEmpty;

  String? get campaignVideo => product.campaignVideo?.firstOrNull;

  bool? get cashbackEnabled =>
      config.appFeaturesConfig.cashBackConfig?.isEnableTagPDP;

  num get bestInstallment => product.bestInstallment.value ?? 0;

  PDPModel copyWith({
    Product? product,
    Config? config,
    bool? featureItem,
    Item? selectedSize,
    bool? isGiftCard,
    bool? canScroll,
    bool? showBottomInfo,
    bool? hasMessage,
    int? activeItemIndex,
    String? favoriteAnimationMain,
    String? favoriteAnimationDetail,
    CmsProductPdpDocument? pdpDocument,
    bool? isOnComboStepsMode,
    GlobalKey? comboStepsKey,
  }) {
    // Se selectedSize não for passado e o produto mudou, recalcula selectedSize
    Item? newSelectedSize = manageSelectedSize(selectedSize, product, config);

    return PDPModel(
      product: product ?? this.product,
      config: config ?? this.config,
      featureItem: featureItem ?? this.featureItem,
      selectedSize: newSelectedSize ?? this.selectedSize,
      isGiftCard: isGiftCard ?? this.isGiftCard,
      canScroll: canScroll ?? this.canScroll,
      showBottomInfo: showBottomInfo ?? this.showBottomInfo,
      hasMessage: hasMessage ?? this.hasMessage,
      favoriteAnimationMain:
          favoriteAnimationMain ?? this.favoriteAnimationMain,
      favoriteAnimationDetail:
          favoriteAnimationDetail ?? this.favoriteAnimationDetail,
      activeItemIndex: activeItemIndex ?? this.activeItemIndex,
      pdpDocument: pdpDocument ?? this.pdpDocument,
      isOnComboStepsMode: isOnComboStepsMode ?? this.isOnComboStepsMode,
      comboStepsKey: comboStepsKey ?? this.comboStepsKey,
    );
  }

  Item? manageSelectedSize(
      Item? selectedSize, Product? product, Config? config) {
    Item? newSelectedSize = selectedSize;

    final newProduct = product ?? this.product;
    final newConfig = config ?? this.config;

    if (selectedSize == null && newProduct != this.product) {
      if (newProduct.items != null &&
          newProduct.items?.length == 1 &&
          newProduct.sizeTable.length == 1 &&
          newConfig.appFeaturesConfig.enableAutoSelectSize) {
        newSelectedSize = newProduct.items?.first;
      }
    }

    return newSelectedSize;
  }

  /// Força valores nulos para campos opcionais (útil quando copyWith ignora `null`)
  PDPModel withNullableValue({
    bool resetSelectedSize = false,
    bool resetFavoriteAnimationMain = false,
    bool resetFavoriteAnimationDetail = false,
    bool resetPdpDocument = false,
  }) {
    return PDPModel(
      product: product,
      config: config,
      featureItem: featureItem ?? featureItem,
      selectedSize: resetSelectedSize ? null : selectedSize,
      isGiftCard: isGiftCard,
      canScroll: canScroll,
      showBottomInfo: showBottomInfo,
      hasMessage: hasMessage,
      favoriteAnimationMain:
          resetFavoriteAnimationMain ? null : favoriteAnimationMain,
      favoriteAnimationDetail:
          resetFavoriteAnimationDetail ? null : favoriteAnimationDetail,
      activeItemIndex: activeItemIndex,
      pdpDocument: resetPdpDocument ? null : pdpDocument,
      isOnComboStepsMode: isOnComboStepsMode,
      comboStepsKey: comboStepsKey,
    );
  }

  @override
  bool operator ==(covariant PDPModel other) {
    if (identical(this, other)) return true;

    return other.product.productId == product.productId &&
        other.featureItem == featureItem &&
        other.selectedSize == selectedSize &&
        other.isGiftCard == isGiftCard &&
        other.canScroll == canScroll &&
        other.showBottomInfo == showBottomInfo &&
        other.hasMessage == hasMessage &&
        other.activeItemIndex == activeItemIndex &&
        other.pdpDocument == pdpDocument &&
        other.isOnComboStepsMode == isOnComboStepsMode &&
        other.comboStepsKey == comboStepsKey;
  }

  @override
  int get hashCode {
    return product.hashCode ^
        featureItem.hashCode ^
        selectedSize.hashCode ^
        isGiftCard.hashCode ^
        canScroll.hashCode ^
        showBottomInfo.hashCode ^
        hasMessage.hashCode ^
        activeItemIndex.hashCode ^
        pdpDocument.hashCode ^
        isOnComboStepsMode.hashCode ^
        comboStepsKey.hashCode;
  }
}

class PDPNotifier extends ValueNotifier<PDPModel> {
  PDPNotifier(super.value);

  Product get product => value.product;

  String? get productId => value.product.productId;

  GlobalKey? get comboStepsKey => value.comboStepsKey;

  num get numberOfInstallments =>
      value.product.bestInstallment.numberOfInstallments ?? 0;

  bool get shouldDisplayBottom =>
      numberOfInstallments > 0 && value.product.hasAnySizeSellerWithoutError;

  bool get shouldDisplayCashbackTag =>
      value.cashbackEnabled == true &&
      numberOfInstallments != 0 &&
      value.bestInstallment != 0;

  PDPStampCollectionCmsComponent? get stampCollection =>
      value.pdpDocument?.attributes.pdpStampCollectionCmsComponent;

  bool get showAllStamps => getProductsStampsByProduct().length <= 4;

  bool get isOnComboStepsMode => value.isOnComboStepsMode;

  List<PDPStampItem> takeStamps(List<PDPStampItem> stamps, int amount) {
    if (stamps.isEmpty) return [];

    return stamps.take(amount).toList();
  }

  List<PDPStampItem> getProductsStampsByProduct([int? amount]) {
    final stamps = stampCollection?.stamps;

    if (stamps == null || stamps.isEmpty) return [];

    final filteredTags = stamps
        .where((stamp) =>
            product.isFromCategoriesOrClusters([stamp.filterCategoryOrCluster]))
        .toList();

    if (filteredTags.isEmpty) return [];

    return amount != null ? filteredTags.take(amount).toList() : filteredTags;
  }

  void toggleComboStepsMode(bool isOnComboStepsMode) {
    value = value.copyWith(isOnComboStepsMode: isOnComboStepsMode);
  }

  void setComboStepsKey(GlobalKey key) {
    value = value.copyWith(comboStepsKey: key);
  }

  Future<void> loadSelectedProduct(
    CatalogController catalogController,
    GiftCardProductConfig? giftCardProductConfig,
  ) async {
    try {
      final selectedProduct = catalogController.selectedProduct.value;

      if (selectedProduct != null &&
          selectedProduct.productId.isNotNullOrEmpty) {
        final product = await catalogController.getProductById(
          productId: int.parse(selectedProduct.productId!),
          loadSimilarProducts: false,
        );

        final isGiftCard = product.isGiftCard(giftCardProductConfig);

        value = value
            .copyWith(
              product: product,
              isGiftCard: isGiftCard,
            )
            .withNullableValue(
              resetSelectedSize: true,
            );

        catalogController.selectedProduct.value = value.product;
      }
    } catch (e, st) {
      debugPrint('Erro ao carregar produto: $e');
      debugPrintStack(stackTrace: st);
    }
  }

  Future<void> loadSimilarProduct(
    PdpTheme pdpTheme,
    CatalogController catalogController,
  ) async {
    try {
      final productId = value.product.productId;
      final refCode = value.product.productReferenceCode;

      if (pdpTheme.getSimilarProductsFromRefId! && refCode.isNotNullOrEmpty) {
        await catalogController.getSimilarProduct(referenceId: refCode);
      } else if (productId.isNotNullOrEmpty) {
        await catalogController.getSimilarProduct(
          productId: int.parse(productId!),
        );
      }
    } catch (e) {
      debugPrint('Erro ao carregar produtos similares: $e');
      debugPrintStack();
    }
  }

  Future<CmsProductPdpDocument?> loadCMSContent(CmsService cmsService) async {
    try {
      CmsProductPdpDocument? response;

      if (value.pdpDocument == null) {
        response = await cmsService.loadDocument<CmsProductPdpDocument>(
          'product-pdp',
          documentParser: CmsProductPdpDocument.fromJson,
        );
      }

      value = value.copyWith(pdpDocument: response);

      return value.pdpDocument;
    } catch (e) {
      debugPrint('Erro ao carregar conteudo do CMS: $e');
      debugPrintStack();
    }

    return null;
  }

  void showBottomBar({required bool show}) {
    if (value.showBottomInfo != show) {
      value = value.copyWith(showBottomInfo: show);
    }
  }

  void canScroll(bool toggle) {
    value = value.copyWith(canScroll: toggle);
  }

  void updatePaginationPosition(int page) {
    value = value.copyWith(activeItemIndex: page);
  }

  void selectSize(
    AnalyticsEventDispatcher analyticsEventDispatcher,
    Item size,
  ) {
    value = value.copyWith(selectedSize: size);

    analyticsEventDispatcher.dispatchlogViewItemEvent(
      product: value.product,
      screenClass: 'Pdp',
      screenName: 'Detalhes do produto',
      featureItem: value.featureItem ?? false,
    );
  }

  void dismissCallbackWarnMe() {
    value = value.copyWith(selectedSize: null);
  }

  void addToBagFunction(
    ImageProvider<Object>? bottomSheetGiftCardImage,
    CatalogController catalogController,
  ) {
    if (value.isGiftCard) {
      customShowBottomSheet(
        value.config.appFeaturesConfig.giftCardProductConfig?.isEGiftCard ==
                true
            ? SMBottomSheetEGiftCard(
                giftCard: value.product,
                image: bottomSheetGiftCardImage,
                featureItem: value.featureItem ?? false,
              )
            : SMBottomSheetBuyGiftCard(
                giftCard: value.product,
                image: bottomSheetGiftCardImage,
                featureItem: value.featureItem ?? false,
              ),
      );

      return;
    }

    _addToBagSizeSelection(catalogController);
  }

  void _addToBagSizeSelection(CatalogController catalogController) {
    String? itemListName;

    if (navigationArguments is Map &&
        navigationArguments['item_list_name'] != null) {
      itemListName = navigationArguments['item_list_name'];
    }

    if (catalogController.selectedProduct.value == null) {
      return;
    }

    customShowBottomSheet(
      SMBottomSheetAddToBag.forProduct(
        screenClass: 'Pdp',
        catalogController.selectedProduct.value!,
        featureItem: value.featureItem ?? false,
        selectedSize: value.selectedSize,
        itemListName: itemListName,
      ),
    );
  }

  bool get shoudlDisplayBanners {
    return value.config.appFeaturesConfig.dressingRoom &&
        (value.product.campaignVideo?.firstOrNull != null ||
            value.product.banners.isNotEmpty);
  }
}

class PDPProvider<T extends Listenable> extends InheritedNotifier<T> {
  const PDPProvider({
    super.key,
    required super.child,
    required super.notifier,
  });

  static T of<T extends Listenable>(BuildContext context) {
    final provider =
        context.dependOnInheritedWidgetOfExactType<PDPProvider<T>>();

    if (provider == null) {
      throw Exception("No Provider found in context");
    }

    final notifier = provider.notifier;

    if (notifier == null) {
      throw Exception("No notifier found in Provider");
    }

    return notifier;
  }
}
