import 'dart:async';

import 'package:flutter/material.dart';
import 'package:soma_core/modules/catalog/models/product/item.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';
import 'package:soma_core/modules/sizebay/sizebay.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import '../../warn_me/bottom_sheet/sm_bottom_sheet_warn_me.dart';

class SmPdpSizeSection extends StatefulWidget {
  const SmPdpSizeSection({
    super.key,
    required this.product,
    this.selectedSize,
    required this.dismissCallbackWarnMe,
  });

  final Product product;
  final Item? selectedSize;
  final VoidCallback dismissCallbackWarnMe;

  @override
  State<SmPdpSizeSection> createState() => _SmPdpSizeSectionState();
}

class _SmPdpSizeSectionState extends State<SmPdpSizeSection>
    with
        DesignTokensStateMixin,
        AnalyticsEventDispatcherStateMixin,
        AppRoutesStateMixin,
        SomaCoreStateMixin {
  late Product _product;
  late Item? _selectedSize;
  late VoidCallback _dismissCallbackWarnMe;

  @override
  void initState() {
    super.initState();

    _product = widget.product;
    _selectedSize = widget.selectedSize;
    _dismissCallbackWarnMe = widget.dismissCallbackWarnMe;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (config.appFeaturesConfig.virtualDressesRoom?.isEnabled == true) {
      _createSizebayUrl();
    }
  }

  @override
  void didUpdateWidget(covariant SmPdpSizeSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.product != widget.product ||
        oldWidget.selectedSize != widget.selectedSize) {
      setState(() {
        _product = widget.product;
        _selectedSize = widget.selectedSize;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final notifier = PDPProvider.of<PDPNotifier>(context);
    final isOneSizeFitsAll = _product.items?.first.itemSize == 'UNI';
    final hasSizedbay =
        config.appFeaturesConfig.virtualDressesRoom?.isEnabled == true &&
            sizeBayController.hasSizebayUrl;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: tokens.spacingInline.md),
      child: Column(
        children: [
          SMSizeSelection(
            onTapWarnMe: _showWarnMeModal,
            product: notifier.product,
            itemsList: notifier.product.items!,
            selectedSize: _selectedSize,
            onTapTableSizes: onTapTableSizes,
            onTapMySizeIsOver: onTapMySizeIsOver,
            onSelectSize: (value) {
              notifier.selectSize(context.analyticsEventDispatcher, value);

              setState(() {
                _selectedSize = value;
              });
            },
          ),
          SmPluggableWidgets<PdpBelowSizeSectionPlugin>.builder(
            itemBuilder: (context, plugin) => plugin.build(context, _product),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ...ModuleRegistry.root
                  .pluginsOfType<PpdSizeSectionActionPlugin>()
                  .map((p) => p.build(context, _product)),
              if (isOneSizeFitsAll == false && hasSizedbay) ...[
                SmCallToActionLabelTwoLineArrowRight(
                  labelLineOne: 'Provador',
                  labelLineTwo: 'Virtual',
                  onTap: () async => returnSizebayUrl(true),
                ),
              ],
              if (hasSizedbay) ...[
                SmCallToActionLabelTwoLineArrowRight(
                  labelLineOne:
                      tokens.termsAndMessages.measurementTableLineOneLabel ??
                          'Medidas',
                  labelLineTwo:
                      tokens.termsAndMessages.measurementTableLineTwoLabel ??
                          'Corporais',
                  onTap: () async => returnSizebayUrl(false),
                ),
              ]
            ],
          ),
          if (notifier.value.isGiftCard == false) ...[
            SmPluggableWidgets<PdpSizeSectionSubSectionPlugin>.builder(
              itemBuilder: (context, plugin) => plugin.build(context, _product),
            ),
          ]
        ],
      ),
    );
  }

  bool get isOneSizeFitsAll => _product.items?.first.itemSize == 'UNI';

  bool get hasSizedbay =>
      config.appFeaturesConfig.virtualDressesRoom?.isEnabled == true &&
      sizeBayController.hasSizebayUrl;

  Future<void> onTapMySizeIsOver() async {
    _selectSizeForWarnMe().then((selectedSizeSku) {
      if (selectedSizeSku != null) {
        _showWarnMeModal(selectedSizeSku);
      }
    });
  }

  Future<void> onTapTableSizes() async {
    if (mounted) {
      TableMeasures? measures;

      try {
        measures = await measureController.getTableMeasures(
            codeProduct: _product.productReferenceCode?.split('_').first ?? '');
      } catch (e) {
        measures = null;
      }

      dispatchSelectContentEvent('medidas-da-peca', product: _product);

      customShowBottomSheet(
        SMBottomSheetSizeTable(
          product: _product,
          measures: measures,
          selectedSize: _selectedSize?.tamanho![0],
        ),
      );
    }
  }

  Future<SizebayUrls?> _createSizebayUrl() async {
    try {
      SizebayUrls sizebayUrl;

      final productUrl = _product.productUrl;
      final vtexUrl = productUrl?.split('/')[2];

      final productWithHeringUrl =
          productUrl?.replaceAll(vtexUrl!, 'secure.hering.com.br');

      sizebayUrl = await sizeBayController.createUrls(productWithHeringUrl!);

      return sizebayUrl;
    } catch (e) {
      return null;
    }
  }

  Future<void> returnSizebayUrl(bool isVfr) async {
    final url = await _createSizebayUrl();

    if (url != null) {
      navigateToNamed(
        webviewRoute,
        arguments: isVfr
            ? WebViewParams(
                url: url.vfrUrl,
                screenName: 'Provador Virtual',
                avoidKeyboard: false,
              )
            : WebViewParams(url: url.chartUrl, screenName: 'Medidas Corporais'),
      );
    }
  }

  void _showWarnMeModal(String? skuId) {
    if (warnMeTheme.showWarnMe) {
      customShowBottomSheet(
        SMBottomSheetWarnMe(
          skuId: skuId ?? '',
          dismissCallBack: _dismissCallbackWarnMe,
        ),
        enableDrag: false,
      );
    }
  }

  Future<String?> _selectSizeForWarnMe() {
    final completer = Completer<String?>();
    customShowBottomSheet(
      SMBottomSheetChooseSize(
        sizes: _product.items!.where((s) => !s.isAvailable).toList(),
        onSizeSelected: (size) => completer.complete(size.itemId),
        isButtonDisabledFn: (item) => item == null,
        hasSupportText: false,
      ),
    )
        .then((_) => completer.isCompleted ? null : completer.complete(null))
        .catchError(
          (e) => completer.isCompleted ? null : completer.completeError(e),
        );

    return completer.future;
  }
}

abstract class PpdSizeSectionActionPlugin implements Plugin {
  Widget build(BuildContext context, Product product);
}

abstract class PdpSizeSectionSubSectionPlugin implements Plugin {
  Widget? build(BuildContext context, Product product);
}

abstract class PdpBelowSizeSectionPlugin implements Plugin {
  Widget? build(BuildContext context, Product product);
}
