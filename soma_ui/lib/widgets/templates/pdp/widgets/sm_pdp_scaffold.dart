import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/templates/pdp/combo_steps/models/combo_item.dart';
import 'package:soma_ui/widgets/templates/pdp/combo_steps/sm_pdp_combo_controller.dart';
import 'package:soma_ui/widgets/templates/pdp/combo_steps/sm_pdp_combo_steps_page_view.dart';
import 'package:soma_ui/widgets/templates/pdp/combo_steps/utils/add_combo_items_to_bag.dart';

import 'package:soma_ui/widgets/templates/pdp/widgets/sm_pdp_bottom.dart';
import 'package:soma_ui/widgets/templates/pdp/widgets/sm_pdp_details.dart';
import 'package:soma_ui/widgets/templates/pdp/widgets/sm_pdp_main.dart';

class PDPScaffold extends StatefulWidget {
  const PDPScaffold({super.key});

  @override
  State<PDPScaffold> createState() => _PDPScaffoldState();
}

class _PDPScaffoldState extends State<PDPScaffold>
    with DesignTokensStateMixin, SomaCoreStateMixin, AppAssetsStateMixin {
  final scrollController = ScrollController();
  late StreamSubscription<Product?> streamSelectedProduct;

  bool canScroll = true;
  final events = [];

  @override
  void initState() {
    super.initState();

    scrollController.addListener(_onScroll);

    Timer? debounceTimer;
    scrollController.addListener(() => _onScrollDebounce(debounceTimer));

    scheduleMicrotask(() async {
      final notifier = PDPProvider.of<PDPNotifier>(context);

      await notifier.loadSelectedProduct(
        catalogController,
        giftCardProductConfig,
      );
      await notifier.loadSimilarProduct(pdpTheme, catalogController);

      await notifier.loadCMSContent(context.locateService<CmsService>());

      _addToStorage();
    });

    streamSelectedProduct =
        catalogController.selectedProduct.listen((product) async {
      final notifier = PDPProvider.of<PDPNotifier>(context);
      catalogController.similarProducts.value = null;
      if (product != null && notifier.productId != product.productId) {
        _scrollToTop();
        await notifier.loadSelectedProduct(
            catalogController, giftCardProductConfig);
        _addToStorage();
        await notifier.loadSimilarProduct(pdpTheme, catalogController);
      }
    });
  }

  Future<void> _handleComboStepsAddToBag() async {
    final comboController = context.locateService<SMPdpComboController>();

    if (comboController.isComboComplete) {
      return await PDPComboItemsUtils.addComboItems(
        context,
        clearCombo: comboController.clearCombo,
        itemsController: itemsController,
      );
    }

    if (comboController.hasCurrentStepIndexSelectedItem &&
        comboController.currentStepSelectedItem.value != null) {
      return _addItemToCombo(comboController.currentStepSelectedItem.value!);
    }
    _scrollToComboStepsPageView();
    return;
  }

  Future<void> _addItemToCombo(ComboItem currentSelectedStepItem) async {
    final comboStepsKey = PDPProvider.of<PDPNotifier>(context).comboStepsKey;
    final comboStepsState = comboStepsKey?.currentState;

    if (comboStepsState != null &&
        comboStepsState.mounted &&
        comboStepsState is SMPDPComboStepsPageViewState) {
      return await comboStepsState.addItemToCombo(
        currentSelectedStepItem.product,
        currentSelectedStepItem.item,
        currentSelectedStepItem.step,
      );
    }
  }

  void _scrollToComboStepsPageView() {
    final notifier = PDPProvider.of<PDPNotifier>(context);
    final comboStepsKey = notifier.comboStepsKey;

    if (comboStepsKey == null) return;

    final contextFromComboSteps = comboStepsKey.currentContext;
    if (contextFromComboSteps != null) {
      contextFromComboSteps
          .locateService<SMPdpComboController>()
          .setVisible(true);
      Scrollable.ensureVisible(
        contextFromComboSteps,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    scrollController.dispose();
    streamSelectedProduct.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifier = PDPProvider.of<PDPNotifier>(context);

    final mediaQuery = MediaQuery.of(context);

    final fixedHeader = ModuleRegistry.root
        .mapPluginsOfType<PdpFixedHeaderPlugin, Widget>(
            (p) => p.build(context, notifier.product))
        .firstOrNull;

    final mainSize = fixedHeader != null
        ? Size(
            mediaQuery.size.width,
            // O tamanho é diminuído para a imagem não cobrir a tela toda e
            // o usuário ver que pode scrollar para baixo.
            mediaQuery.size.height - spacingStack.xl,
          )
        : mediaQuery.size;

    Widget scaffold = SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.only(bottom: 72),
            controller: scrollController,
            scrollDirection: Axis.vertical,
            physics: notifier.value.canScroll
                ? const ScrollPhysics()
                : const NeverScrollableScrollPhysics(),
            child: Listener(
              onPointerDown: (event) => events.add(event.pointer),
              onPointerUp: (event) {
                events.clear();
                notifier.canScroll(true);
              },
              onPointerMove: (event) {
                if (events.length == 2) {
                  notifier.canScroll(false);
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox.fromSize(
                    size: mainSize,
                    child: const SMPDPMain(),
                  ),
                  SMPDPDetails(scrollToTop: _scrollToTop)
                ],
              ),
            ),
          ),
          if (notifier.shouldDisplayBottom)
            SMPDPBottom(
                selectedProduct: notifier.value.product,
                showBottomInfo: notifier.value.showBottomInfo,
                addToBagFunction: () async {
                  final isOnComboStepsMode = notifier.isOnComboStepsMode;
                  if (isOnComboStepsMode) {
                    return await _handleComboStepsAddToBag();
                  }
                  return notifier.addToBagFunction(
                    bottomSheetGiftCardImage,
                    catalogController,
                  );
                }),
        ],
      ),
    );

    if (fixedHeader != null) {
      scaffold = MediaQuery(
        data: mediaQuery.copyWith(
          padding: mediaQuery.padding.copyWith(
            // Tamanho fixo para espaçamento entre a NavBar e o Header fixo
            top: spacingStack.xl,
          ),
        ),
        child: scaffold,
      );
    }

    return Column(
      children: [
        if (fixedHeader != null) fixedHeader,
        Expanded(child: scaffold),
      ],
    );
  }

  void _onScroll() {
    final notifier = PDPProvider.of<PDPNotifier>(context);
    if (notifier.isOnComboStepsMode) {
      return _comboModeScrollHandler(notifier);
    }

    return notifier.showBottomBar(
      show: mounted && scrollController.offset > pdpTheme.showBottomBarAtHeight,
    );
  }

  void _comboModeScrollHandler(PDPNotifier notifier) {
    if (!mounted) return;

    final comboController = context.locateService<SMPdpComboController>();

    final currentScrollOffset = scrollController.offset;

    final isComboVisible = comboController.isVisible.value;

    if (!isComboVisible && currentScrollOffset > 450) {
      return notifier.showBottomBar(show: true);
    }

    return notifier.showBottomBar(show: false);
  }

  void _onScrollDebounce(Timer? timer) {
    final provider = PDPProvider.of<PDPNotifier>(context);
    timer?.cancel();
    timer = Timer(const Duration(milliseconds: 100), () {
      final showCondition =
          scrollController.offset > (MediaQuery.of(context).size.height / 2);
      if (provider.isOnComboStepsMode) {
        return _comboModeScrollHandler(provider);
      }

      return provider.showBottomBar(show: showCondition);
    });
  }

  void _scrollToTop() {
    scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 900),
      curve: Curves.linear,
    );
  }

  void _addToStorage() {
    if (appFeaturesConfig.storeLastSeenProducts && mounted) {
      context.locateService<LastSeenProductsStorage>().addProduct(
          PDPProvider.of<PDPNotifier>(context).value.product.toJson());
    }
  }
}
