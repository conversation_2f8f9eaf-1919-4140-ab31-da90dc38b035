import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_core/modules/catalog/models/product/item.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/shipping_options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/slas.dart';
import 'package:soma_core/modules/checkout/services/address_service.dart';

typedef SelectSizeFn = Future<Item?> Function();

class SMPDPDeliveryPreview extends StatefulWidget {
  final Product product;
  final Item? selectedSize;
  final SelectSizeFn selectSize;
  final bool showCalculateButton;
  final String? brand;
  final TextStyle? titleStyle;

  const SMPDPDeliveryPreview({
    super.key,
    required this.selectedSize,
    required this.selectSize,
    required this.product,
    this.showCalculateButton = true,
    this.brand,
    this.titleStyle,
  });

  @override
  State<SMPDPDeliveryPreview> createState() => _SMPDPDeliveryPreviewState();
}

class _SMPDPDeliveryPreviewState extends State<SMPDPDeliveryPreview>
    with
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  static const _invalidCepMessage = 'Ops! Tem algo de errado com este CEP!';
  static const _cannotBeDeliveredErrorMessage =
      'Esse item não tem opções de frete';
  static const _cannotCalculateDeliveryErrorMessage =
      'Não foi possível calcular o frete';

  final _cepTextController = TextEditingController();
  final _cepFocusNode = FocusNode();

  final _cepRegex = RegExp(r'^\d{5}-\d{3}$');
  ShippingOptionsNew? _shippingOptions;
  String? _errorMessage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    _cepTextController.text = MaskTextInputFormatter(
      mask: '#####-###',
    ).maskText(addressController.hasPostalCode
        ? addressController.addressInfo.value!.postalCode!
        : (orderForm?.shippingData?.address?.postalCode ?? ''));
  }

  @override
  void didUpdateWidget(covariant SMPDPDeliveryPreview oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.selectedSize?.itemId != oldWidget.selectedSize?.itemId) {
      setState(() {
        _shippingOptions = null;
      });

      if (widget.selectedSize != null &&
          _cepRegex.hasMatch(_cepTextController.text)) {
        _queryShippingOptions();
      }
    }
  }

  @override
  void dispose() {
    _cepTextController.dispose();
    _cepFocusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          textTransform.body(termsAndMessages.productShippingTitle),
          style: widget.titleStyle ?? typography.typeStyles.subtitle,
        ),
        SizedBox(height: spacingStack.md),
        _buildCepInput(),
        if (widget.brand != null && pdpTheme.showProductBrandSender)
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(top: spacingStack.xs),
                  child: SMTag(
                    tagHeight: TagHeight.medium,
                    text: 'Esse produto será entregue por ${widget.brand}',
                    textColor: colors.typography.pure2,
                    backgroundColor: colors.neutral.dark1,
                    textStyle: typography.typeStyles.bodySm,
                  ),
                ),
              ),
            ],
          ),
        if (_shippingOptions != null &&
            (_shippingOptions?.onePackAllOptions ?? []).isNotEmpty) ...[
          SizedBox(height: spacingStack.md),
          _ShippingOptionsPreview(shippingOptions: _shippingOptions!),
        ],
      ],
    );
  }

  Widget _buildCepInput() {
    return GestureDetector(
      onTap: widget.selectedSize == null
          ? () {
              if (pdpTheme.deliveryPreviewBehavior ==
                  PdpDeliveryPreviewBehavior.forceSizeSelection) {
                _selectSize();
              } else {
                setState(() {
                  _errorMessage = 'por favor, selecione antes um tamanho';
                });
              }
            }
          : null,
      child: AbsorbPointer(
        absorbing: widget.selectedSize == null,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: SmSensitiveData(
                child: SMCepInput(
                  controller: _cepTextController,
                  focusNode: _cepFocusNode,
                  supportText: _errorMessage,
                  isError: _errorMessage != null,
                  isLoading: _isLoading,
                  onChange: (cep) {
                    if (cep.length < 9) {
                      setState(() {
                        _shippingOptions = null;
                        _errorMessage = null;
                      });
                      return;
                    }

                    _queryShippingOptions();
                    dispatchSelectContentEvent(
                      'calcular_frete',
                      product: widget.product,
                    );
                    FocusScope.of(context).unfocus();
                  },
                  onSubmit: (_) {
                    _queryShippingOptions();

                    FocusScope.of(context).unfocus();
                  },
                ),
              ),
            ),
            if (widget.showCalculateButton) ...[
              const SizedBox(width: 8),
              Expanded(
                child: SMButton.primary(
                  size: ButtonSize.medium,
                  onPressed: () {
                    _queryShippingOptions();
                    analyticsController.logFreightCalculate();
                    FocusScope.of(context).unfocus();
                  },
                  child: Text(textTransform.button('calcular')),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }

  void _selectSize() async {
    try {
      final size = await widget.selectSize();
      if (size != null) {
        _cepFocusNode.requestFocus();
      }
    } catch (e) {
      debugPrint('Erro ao selecionar tamanho: ${e.toString()}');
    }
  }

  void _queryShippingOptions() async {
    try {
      setState(() {
        _isLoading = true;
        _shippingOptions = null;
        _errorMessage = null;
      });

      if (widget.selectedSize == null || !widget.selectedSize!.isAvailable) {
        return;
      }

      final cep = _cepTextController.text.trim();
      if (_cepRegex.hasMatch(cep) == false) {
        setState(() {
          _errorMessage = _invalidCepMessage;
        });

        return;
      }

      final shippingOptions = await addressController.simulateShippingOptions(
        postalCode: cep,
        products: [
          SimulationProducts(
            id: widget.selectedSize!.itemId!,
            seller: widget.selectedSize!.firstAvailableSeller?.sellerId ?? '',
          ),
        ],
      );

      if (mounted) {
        setState(() {
          _shippingOptions = shippingOptions;

          if ((shippingOptions.onePackAllOptions ?? []).isEmpty) {
            _errorMessage = _cannotBeDeliveredErrorMessage;
          }
        });
      }
    } catch (e, stackTrace) {
      debugPrint('Erro ao simular frete do produto: $e');
      debugPrintStack(stackTrace: stackTrace);

      final String errorMessage;
      if (e is ShippingOptionsSimulationError &&
          e.orderFormMessages.any(
              (m) => ['withoutStock', 'cannotBeDelivered'].contains(m.code))) {
        errorMessage = _cannotBeDeliveredErrorMessage;
      } else {
        errorMessage = _cannotCalculateDeliveryErrorMessage;
      }

      if (mounted) {
        setState(() {
          _errorMessage = errorMessage;
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

class _ShippingOptionsPreview extends StatefulWidget {
  const _ShippingOptionsPreview({required this.shippingOptions});

  final ShippingOptionsNew shippingOptions;

  @override
  State<_ShippingOptionsPreview> createState() =>
      _ShippingOptionsPreviewState();
}

class _ShippingOptionsPreviewState extends State<_ShippingOptionsPreview>
    with DesignTokensStateMixin, SomaCoreStateMixin {
  @override
  Widget build(BuildContext context) {
    final pickupInPoint = widget.shippingOptions.pickupInPoint;
    final shouldAppearEmptyPickup =
        !(pickupInPoint == null && pickupTheme.hideEmptyPickup);

    if (storePickupIsEnabled) {
      return Column(
        children: [
          _buildOptionsRow(
            title: Text(
              textTransform.body('receber em casa'),
              style:
                  pickupTheme.deliveryPreviewTheme.shippingOptionsTextStyle ??
                      typeStyles.body.copyWith(color: colors.typography.pure2),
            ),
            options: _buildShippingOptions(
                showHomeDeliveryTitle:
                    tokens.pdpTheme.deliveryInRowLayoutEnabled),
          ),
          if (shouldAppearEmptyPickup) ...[
            SizedBox(
              height: tokens.pdpTheme.deliveryInRowLayoutEnabled
                  ? spacingStack.xs
                  : spacingStack.xl,
            ),
            _buildOptionsRow(
              title: Text(
                textTransform.body(
                    "retirar${tokens.pdpTheme.deliveryInRowLayoutEnabled ? ' ' : '\n'}na loja"),
                style: pickupTheme
                        .deliveryPreviewTheme.shippingOptionsTextStyle ??
                    typeStyles.body.copyWith(color: colors.typography.pure2),
              ),
              pickup: true,
              options: _buildPickupOptions(),
            ),
          ]
        ],
      );
    }

    return Column(
      children: [
        _buildShippingOptions(
            showHomeDeliveryTitle: tokens.pdpTheme.deliveryInRowLayoutEnabled),
      ],
    );
  }

  Widget _buildOptionsRow({
    required Widget title,
    required Widget options,
    bool pickup = false,
  }) {
    final pickupInPoint = widget.shippingOptions.pickupInPoint;
    final alternateLayout = tokens.pdpTheme.deliveryInRowLayoutEnabled;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!alternateLayout) ...[
          Expanded(child: title),
          if (config.appFeaturesConfig.shippingOptionsConfig.showAllOptions !=
              true)
            SizedBox(width: spacingInline.sm),
        ],
        Expanded(
          flex: !alternateLayout ? 4 : 1,
          child: pickup &&
                  (pickupInPoint == null ||
                      (pickupInPoint.pickupPoints ?? []).isEmpty) &&
                  alternateLayout
              ? Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    title,
                    SizedBox(height: tokens.spacingInset.sm),
                    options,
                  ],
                )
              : Container(
                  padding: !alternateLayout
                      ? EdgeInsets.only(left: spacingInline.sm)
                      : null,
                  decoration: !alternateLayout
                      ? BoxDecoration(
                          border: Border(
                              left: BorderSide(color: colors.neutral.light2)),
                        )
                      : null,
                  child: options,
                ),
        ),
      ],
    );
  }

  Widget _buildPickupOptions() {
    final pickupInPoint = widget.shippingOptions.pickupInPoint;
    if (pickupInPoint == null || (pickupInPoint.pickupPoints ?? []).isEmpty) {
      return Text(
        textTransform.body(
            'Esse produto não está disponível em nenhuma loja perto de você, mas você ainda pode receber ele na sua casa.'),
        style: TextStyle(
            fontFamily: typography.fontFamilyPrimary,
            color: tokens.pdpTheme.deliveryInRowLayoutEnabled
                ? colors.typography.pure2
                : null),
      );
    }
    return _PickupOptionsList(pickupInPoint.pickupPoints!);
  }

  Widget _buildShippingOptions({bool showHomeDeliveryTitle = false}) {
    final shippingOptionsConfig =
        config.appFeaturesConfig.shippingOptionsConfig;
    final valuesReader = shippingOptionsConfig.valuesReader;

    return AllShippingOptions(
      allOptions: widget.shippingOptions.onePackAllOptions ?? [],
      showHomeDeliveryTitle: showHomeDeliveryTitle,
      valuesReader: valuesReader,
    );
  }
}

class AllShippingOptions extends StatelessWidget {
  const AllShippingOptions({
    required this.allOptions,
    required this.showHomeDeliveryTitle,
    required this.valuesReader,
    super.key,
  });

  final List<OptionsNew> allOptions;
  final bool showHomeDeliveryTitle;
  final ShippingOptionValuesReader valuesReader;

  @override
  Widget build(BuildContext context) {
    List<Widget> listOptionsWidget = allOptions.map((option) {
      return _ShippingOption(
        option: option,
        title: option.title ?? "",
        inRowLayout: showHomeDeliveryTitle,
      );
    }).toList();

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: listOptionsWidget,
    );
  }
}

class _ShippingOption extends StatelessWidget {
  const _ShippingOption({
    required this.option,
    required this.title,
    required this.inRowLayout,
    this.titleColor,
  });

  final OptionsNew option;
  final String title;
  final bool inRowLayout;
  final Color? titleColor;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final config = context.locateService<Config>();
    final showAllOptions =
        config.appFeaturesConfig.shippingOptionsConfig.showAllOptions ?? false;
    final effectiveWidthProportion = showAllOptions ? 0.16 : 0.2;

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: tokens.spacingStack.xs,
      ),
      child: SMListItem(
        leading: SizedBox(
          width: MediaQuery.of(context).size.width * effectiveWidthProportion,
          child: Text(
            tokens.textTransform.body(option.getShippingPrice),
            style: inRowLayout
                ? tokens
                    .pickupTheme.deliveryPreviewTheme.shippingOptionsTextStyle
                    ?.copyWith(color: tokens.colors.neutral.medium2)
                : null,
          ),
        ),
        title: Text(
          tokens.textTransform.body(title),
          style: tokens
                  .pickupTheme.deliveryPreviewTheme.shippingOptionsTextStyle
                  ?.copyWith(color: titleColor) ??
              tokens.typography.typeStyles.body
                  .copyWith(color: tokens.colors.brand.pure1),
        ),
        subtitle: Text(
          tokens.textTransform.body(option.getShippingEstimatedFormatted ?? ""),
          style: inRowLayout
              ? tokens.typography.typeStyles.body
                  .copyWith(color: tokens.colors.typography.medium1)
              : tokens.typography.typeStyles.bodyXs,
        ),
      ),
    );
  }
}

class _PickupOptionsList extends StatefulWidget {
  final List<Slas> options;

  const _PickupOptionsList(this.options);

  @override
  State<_PickupOptionsList> createState() => __PickupOptionsListState();
}

class __PickupOptionsListState extends State<_PickupOptionsList>
    with DesignTokensStateMixin, SingleTickerProviderStateMixin {
  late final AnimationController _moreOptionsSizeController;
  bool _showAllOptions = false;

  @override
  void initState() {
    super.initState();
    _moreOptionsSizeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _moreOptionsSizeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final listItemAlignment =
        pickupTheme.deliveryPreviewTheme.listItemLeadingAlignment;
    final leadingWidth =
        listItemAlignment == ListItemLeadingAlignment.topItemsAligned
            ? MediaQuery.of(context).size.width * 0.2
            : null;
    final cmsTagController = context.locateService<CmsTagController>();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SMListItem(
          contentAlignment: CrossAxisAlignment.center,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (tokens.pdpTheme.deliveryInRowLayoutEnabled)
                Text(
                  textTransform.body("retire na loja"),
                  style: pickupTheme
                          .deliveryPreviewTheme.shippingOptionsTextStyle ??
                      typeStyles.body.copyWith(color: colors.typography.pure2),
                ),
              Text(
                textTransform.body(
                  termsAndMessages.productPickupOneDay ??
                      'Retire em 1 dia útil',
                ),
                style: typeStyles.body.copyWith(
                  color: tokens.pdpTheme.deliveryInRowLayoutEnabled
                      ? colors.typography.medium1
                      : colors.brand.pure1,
                ),
              ),
            ],
          ),
          leading: SizedBox(
            width: leadingWidth,
            child: Text(
              textTransform.body('grátis'),
              style: tokens.pdpTheme.deliveryInRowLayoutEnabled
                  ? pickupTheme.deliveryPreviewTheme.shippingOptionsTextStyle
                      ?.copyWith(color: colors.neutral.medium2)
                  : null,
            ),
          ),
          trailing: cmsTagController.pickupTag.value != null &&
                  cmsTagController.pickupTag.value!.active
              ? SMTag(
                  text: cmsTagController.pickupTag.value!.title,
                  backgroundColor:
                      cmsTagController.pickupTag.value!.colors.backgroundColor,
                  borderColor:
                      cmsTagController.pickupTag.value!.colors.borderColor,
                  textColor:
                      cmsTagController.pickupTag.value!.colors.titleColor,
                )
              : null,
        ),
        SizedBox(height: spacingStack.xs),
        for (int i = 0; i < min(2, widget.options.length); i++)
          _PickupOptionListItem(widget.options[i]),
        if (widget.options.length > 2) ...[
          SizeTransition(
            sizeFactor: _moreOptionsSizeController,
            axisAlignment: -1,
            child: Column(
              children: [
                for (int i = 2; i < widget.options.length; i++)
                  _PickupOptionListItem(widget.options[i]),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.all(spacingInset.sm),
            child: SMButton.link(
              onPressed: _toggleShowAllOptions,
              child: Text(
                textTransform.body(
                  _showAllOptions ? 'ver menos' : 'ver mais',
                ),
                style: pickupTheme
                        .deliveryPreviewTheme.toggleShowOptionsTextStyle ??
                    typeStyles.body.copyWith(color: colors.typography.pure2),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _toggleShowAllOptions() {
    setState(() {
      _showAllOptions = !_showAllOptions;
      if (_showAllOptions) {
        _moreOptionsSizeController.forward();
      } else {
        _moreOptionsSizeController.reverse();
      }
    });
  }
}

class _PickupOptionListItem extends StatelessWidget {
  final Slas option;

  const _PickupOptionListItem(this.option);

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final pickupDeliveryPreviewTheme = tokens.pickupTheme.deliveryPreviewTheme;
    return Column(
      children: [
        SizedBox(height: tokens.spacingStack.sm),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.friendlyName ??
                        tokens.textTransform.body('Loja desconhecida'),
                    style: pickupDeliveryPreviewTheme.pointOptionTextStyle ??
                        tokens.typography.typeStyles.bodySm,
                  ),
                  Text(
                    option.addressResume ??
                        tokens.textTransform.body('Endereço desconhecido'),
                    style: tokens.pdpTheme.deliveryInRowLayoutEnabled
                        ? tokens.typography.typeStyles.body.copyWith(
                            color: tokens.colors.typography.medium1,
                            fontWeight: FontWeight.w400)
                        : pickupDeliveryPreviewTheme.pointOptionTextStyle ??
                            tokens.typography.typeStyles.bodySm.copyWith(
                              color: tokens.colors.typography.light2,
                            ),
                  ),
                ].separated(SizedBox(height: tokens.spacingStack.xxs)),
              ),
            ),
            SizedBox(width: tokens.spacingInline.md),
            Row(
              children: [
                if (option.distance != null)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: tokens.spacingInline.xs,
                      vertical: 1.0,
                    ),
                    color: pickupDeliveryPreviewTheme.pointDistanceBackground ??
                        tokens.colors.neutral.medium1,
                    child: Text(
                        '${option.distance!.toStringAsFixed(option.distance! > 0 ? 0 : 1)} km',
                        style: tokens.typography.typeStyles.bodySm),
                  ),
                InkWell(
                  onTap: () {
                    Clipboard.setData(
                      ClipboardData(text: option.fullAddress ?? ''),
                    );
                    showSnackBar(
                      SMSnackBarGetX(
                        textLabel: 'endereço da loja copiado',
                        designTokens: tokens,
                      ),
                    );
                  },
                  child: Icon(
                    tokens.icons.copy,
                    size: pickupDeliveryPreviewTheme.copyIconSize,
                  ),
                ),
              ].separated(
                SizedBox(
                  width: pickupDeliveryPreviewTheme.copyIconLeftSpace ??
                      tokens.spacingInline.sm,
                ),
              ),
            ),
            SizedBox(width: tokens.spacingInline.xxs),
          ],
        ),
        SizedBox(height: tokens.spacingStack.sm),
        if (pickupDeliveryPreviewTheme.hideEndingLine != true)
          SMLine(
            lineSize: LineSize.small,
            lineColor: tokens.colors.neutral.medium1,
          ),
      ],
    );
  }
}

abstract class DeliveryOptionPDPPlugins extends Plugin {
  Widget? build(String type);
}
