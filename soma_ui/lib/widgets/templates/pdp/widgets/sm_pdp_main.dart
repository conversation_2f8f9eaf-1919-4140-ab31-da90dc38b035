import 'dart:async';

import 'package:flutter/material.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_ui/widgets/templates/pdp/widgets/index.dart';
import 'package:soma_ui/widgets/templates/pdp/controller/carousel_other_look_controller.dart';

class SMPDPMain extends StatefulWidget {
  const SMPDPMain({super.key});

  @override
  State<SMPDPMain> createState() => _SMPDPMainState();
}

class _SMPDPMainState extends State<SMPDPMain>
    with
        DesignTokensStateMixin,
        TickerProviderStateMixin,
        SomaCoreStateMixin,
        AppAssetsStateMixin {
  late final AnimationController _animationController;
  late final Animation<Offset> _headerAnimation;
  late final Animation<Offset> _footerAnimation;

  final _pageController = PageController();

  bool _showAnimation = true;

  @override
  void initState() {
    super.initState();

    _setAnimationProperties();

    _pageController.addListener(() {
      final int position = _pageController.page!.round();
      PDPProvider.of<PDPNotifier>(context).updatePaginationPosition(position);
    });

    Future.delayed(const Duration(seconds: 1), () {
      _animationController.forward();

      if (mounted) {
        setState(() {
          _showAnimation = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifier = PDPProvider.of<PDPNotifier>(context);
    final carouselOtherLookController =
        context.locateService<CarouselOtherLookController>();

    final showFooter = pdpTheme.mainContent.isImage &&
        (pdpTheme.outOfStockFooter.isDisabled ||
            notifier.numberOfInstallments > 0);

    final futurizaButton = ModuleRegistry.root
        .mapPluginsOfType<PdpMainFurutizaSectionPlugin, Widget>(
            (p) => p.build(context, notifier.productId ?? '', _pageController))
        .firstOrNull;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // CARROSSEL
        Column(
          children: [
            Expanded(
              child: SMPDPCarousel(
                product: notifier.value.product,
                productHeroID: notifier.value.product.productHeroID,
                imageScrollController: _pageController,
              ),
            ),
            if (pdpTheme.mainContent.isImageAndInformation)
              _ProductInformationBelowImage(
                favoriteAnimation: notifier.value.favoriteAnimationMain,
                product: notifier.value.product,
                onPressed: () => notifier.addToBagFunction(
                  bottomSheetGiftCardImage,
                  catalogController,
                ),
              ),
          ],
        ),

        // HEADER FOOTER
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SmObserver(
                  builder: (context) {
                    if (carouselOtherLookController.showNavBar.value == false ||
                        _showAnimation == false) {
                      return const SizedBox.shrink();
                    }

                    return _AnimatedHeader(
                      animation: _headerAnimation,
                      controller: _animationController,
                    );
                  },
                ),
                if (futurizaButton != null &&
                    config.futurizaConfig?.floatingButtonType ==
                        FloatingButtonType.header)
                  futurizaButton
              ],
            ),
            if (showFooter)
              Column(
                children: [
                  if (futurizaButton != null &&
                      config.futurizaConfig?.floatingButtonType ==
                          FloatingButtonType.footer)
                    futurizaButton,
                  if (_showAnimation)
                    _AnimatedFooter(
                      addToBagFunction: () => notifier.addToBagFunction(
                        bottomSheetGiftCardImage,
                        catalogController,
                      ),
                      animation: _footerAnimation,
                      controller: _animationController,
                    ),
                ],
              ),
          ],
        ),
        OverlayButtonAddToTravelSuitcaseAfterFavorite(
          produtc: notifier.value.product,
          sizes: ProductOverlayPluginSizes(width: double.infinity, height: 86),
          position: ProductOverlayPluginPosition(bottom: 0, right: 0, left: 0),
        )
      ],
    );
  }

  void _setAnimationProperties() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerAnimation = Tween(
      begin: const Offset(0.0, -100.0),
      end: const Offset(0.0, 0.0),
    ).animate(_animationController);

    _footerAnimation = Tween(
      begin: const Offset(0.0, 110),
      end: const Offset(0.0, 0.0),
    ).animate(_animationController);
  }
}

class _ProductInformation extends StatelessWidget {
  const _ProductInformation({super.key});

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final pdpTheme = tokens.pdpTheme;
    final notifier = PDPProvider.of<PDPNotifier>(context);

    return Padding(
      padding: EdgeInsets.only(
        top: pdpTheme.productTagsTheme != null
            ? tokens.spacingStack.xs
            : tokens.spacingStack.lg,
        left: tokens.spacingInline.md,
        right: tokens.spacingInline.md,
        bottom: pdpTheme.showBuyButtonOnProductInformation
            ? tokens.spacingInline.xs
            : tokens.spacingInline.md,
      ),
      child: SMProductInformation.fromProduct(
        PDPProvider.of<PDPNotifier>(context).value.product,
        hasFavoriteButton: pdpTheme.favoriteButtonPlacement ==
            FavoriteButtonPlacement.productInformationRight,
        shareButton: pdpTheme.shareButton,
        favoriteAnimation: notifier.value.favoriteAnimationMain,
      ),
    );
  }
}

class _ProductInformationBelowImage extends StatelessWidget {
  const _ProductInformationBelowImage({
    required this.favoriteAnimation,
    required this.product,
    this.onPressed,
  });

  final String? favoriteAnimation;
  final void Function()? onPressed;
  final Product product;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final pdpTheme = tokens.pdpTheme;

    return Column(
      children: [
        if (pdpTheme.productTagsTheme != null)
          Padding(
            padding: EdgeInsets.only(
              top: tokens.spacingStack.sm,
              left: tokens.spacingInline.md,
              right: tokens.spacingInline.md,
              bottom: tokens.spacingInline.xxs,
            ),
            child: SmProductsTags(
              product: product,
              productTagsTheme: pdpTheme.productTagsTheme!,
            ),
          ),
        const _ProductInformation(),
        if (pdpTheme.showBuyButtonOnProductInformation &&
            (product.bestInstallment.numberOfInstallments ?? 0) > 0)
          Padding(
            padding: EdgeInsets.only(
              left: tokens.spacingInline.md,
              right: tokens.spacingInline.md,
              bottom: tokens.spacingInline.xs,
            ),
            child: SMButton.primary(
              onPressed: onPressed,
              size: ButtonSize.small,
              expanded: true,
              style: SMButtonStyle(
                borderColor: tokens.colors.neutral.pure1,
                backgroundColor: tokens.colors.brand.pure1,
                foregroundColor: tokens.colors.typography.pure1,
              ),
              child: Text(
                tokens.textTransform.button(tokens.termsAndMessages.buyButton),
              ),
            ),
          ),
      ],
    );
  }
}

class _AnimatedFooter extends StatelessWidget {
  const _AnimatedFooter({
    required this.addToBagFunction,
    required this.controller,
    required this.animation,
  });

  final VoidCallback addToBagFunction;
  final AnimationController controller;
  final Animation<Offset> animation;

  @override
  Widget build(BuildContext context) {
    final notifier = PDPProvider.of<PDPNotifier>(context);

    return AnimatedBuilder(
      animation: controller,
      child: SMPdpFooter(
        selectedProduct: notifier.value.product,
        activeItemIndex: notifier.value.activeItemIndex,
        addToBagFunction: addToBagFunction,
      ),
      builder: (context, child) {
        return Transform.translate(offset: animation.value, child: child);
      },
    );
  }
}

class _AnimatedHeader extends StatelessWidget {
  const _AnimatedHeader({
    required this.controller,
    required this.animation,
  });

  final AnimationController controller;

  final Animation<Offset> animation;

  @override
  Widget build(BuildContext context) {
    final notifier = PDPProvider.of<PDPNotifier>(context);

    return AnimatedBuilder(
      animation: controller,
      child: SMPDPHeader(product: notifier.value.product),
      builder: (context, child) {
        return Transform.translate(offset: animation.value, child: child);
      },
    );
  }
}

abstract class PdpMainFurutizaSectionPlugin implements Plugin {
  Widget build(BuildContext context, String productId, PageController page);
}
