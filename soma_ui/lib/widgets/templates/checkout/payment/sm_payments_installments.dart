import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_core/modules/checkout/models/index.dart';

import '../../../../soma_ui.dart';

class SMPaymentsInstallmenst extends StatefulWidget {
  const SMPaymentsInstallmenst({
    super.key,
  });

  @override
  State<SMPaymentsInstallmenst> createState() => _SMPaymentsInstallmenstState();
}

class _SMPaymentsInstallmenstState extends State<SMPaymentsInstallmenst>
    with
        AppRoutesStateMixin,
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  List<InstallmentOrderForm> installmentList = [];
  Rxn<InstallmentOrderForm>? installmentOrderFormSelected =
      Rxn<InstallmentOrderForm>(null);

  Future<void> _installmentSelect() async {
    try {
      paymentsController.installment!.value =
          installmentOrderFormSelected!.value;
      await paymentsController.installmentSelect().then(
            (value) => dispatchLogAddPaymentInfoEvent(
              screenClass: 'PaymentsInstallmenst',
              orderForm: paymentsController.orderForm.value!,
              paymentType: paymentsController.payamentMethods[
                  paymentsController.paymentSelected.value!.paymentMethod],
            ),
          );

      navigateToNamed(orderReviewRoute);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void initState() {
    super.initState();
    dispatchLogScreenViewEvent(
      screenClass: 'PaymentsInstallmenst',
      screenName: 'Pagamento parcelado',
    );

    installmentOrderFormSelected?.value = paymentsController.installment?.value;

    var response = paymentsController.getInstallments();
    setState(() {
      installmentList = response;
    });
  }

  Widget _buildInstallmentListItem(InstallmentOrderForm installment) {
    final selectedInstallment = installmentOrderFormSelected!.value ??
        paymentsController.installment!.value;

    var isSelected = selectedInstallment?.count == installment.count &&
        (selectedInstallment?.isValid ?? false) &&
        selectedInstallment?.value == installment.value;

    return Padding(
      padding:
          EdgeInsets.only(top: installment.count == 1 ? 0 : spacingStack.sm),
      child: SMListItem(
        title: Text(textTransform.body(installment.label)),
        subtitle: Text(textTransform.body(installment.description)),
        trailing: SMRadioButton(
          isChecked: installmentOrderFormSelected!.value == installment,
          isDisabled: paymentsController.isLoadingPaymentSelect.value!,
          onTap: () {
            installmentOrderFormSelected!.value = installment;
          },
        ),
        onTap: () {
          setState(() {
            installmentOrderFormSelected!.value = installment;
            isSelected = !isSelected;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final showTitleInNavBar = pagesTheme.showTitleInNavBar;
    final headerTitle = textTransform.title('quer parcelar?');
    return SmObserver(
      builder: (context) => Scaffold(
        body: paymentsController.isLoading.value!
            ? const Center(child: SMSpinner())
            : Column(
                children: [
                  SMNavBar.adaptative(
                    title: showTitleInNavBar ? Text(headerTitle) : null,
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                        spacingInline.md,
                        0,
                        spacingInline.md,
                        MediaQuery.of(context).padding.bottom + spacingStack.xl,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SMMainContent(
                            title: !showTitleInNavBar ? headerTitle : null,
                          ),
                          Expanded(
                            child: ListView(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(
                                    bottom: spacingStack.xxl,
                                  ),
                                  child: Column(
                                    children: installmentList
                                        .map(_buildInstallmentListItem)
                                        .toList()
                                        .separated(
                                          SmHorizontalDivider(
                                            horizontalSpacing: 0,
                                            spacing: 0,
                                            topSpacing: spacingStack.md,
                                          ),
                                        ),
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
        floatingActionButton: Padding(
          padding: EdgeInsets.symmetric(horizontal: spacingInline.md),
          child: SMButton.primary(
            onPressed: _installmentSelect,
            expanded: true,
            isDisabled: installmentOrderFormSelected!.value?.isValid != true,
            isLoading: paymentsController.isLoadingPaymentSelect.value!,
            child: Text(textTransform.button('selecionar parcelamento')),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }
}
