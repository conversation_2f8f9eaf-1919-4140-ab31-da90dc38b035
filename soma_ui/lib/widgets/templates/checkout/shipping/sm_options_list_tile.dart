import 'package:flutter/material.dart';

import 'package:soma_ui/soma_ui.dart';

import 'package:soma_core/modules/checkout/controllers/address_controller.dart';

class SmOptionsListTile extends StatelessWidget {
  final String deliveryPrice;
  final String deliveryTitle;
  final String deliveryTime;
  final String id;
  final bool isSelected;
  final Function() onSelect;
  final bool useSimpleSubtitle;
  final bool deliveryTypeAtTop;
  final String? receiveText;
  final Widget? subtitle;
  final Widget? title;
  final bool hideTrailing;

  const SmOptionsListTile({
    super.key,
    required this.deliveryPrice,
    required this.deliveryTitle,
    required this.deliveryTime,
    required this.id,
    this.isSelected = false,
    required this.onSelect,
    this.useSimpleSubtitle = false,
    this.deliveryTypeAtTop = false,
    this.receiveText,
    this.subtitle,
    this.title,
    this.hideTrailing = false,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    var optionItemTheme = tokens.deliveryTheme.optionItem;
    final effectiveTitleStyle = optionItemTheme?.body.title.textStyle ??
        tokens.typography.typeStyles.subtitle;

    return Row(
      children: [
        Flexible(
          child: Text(
            tokens.textTransform.body(
              deliveryTypeAtTop
                  ? deliveryTitle
                  : receiveText ?? "receber em casa",
            ),
            style: effectiveTitleStyle,
          ),
        ),
        Expanded(
          flex: 4,
          child: Container(
            margin: EdgeInsets.only(left: tokens.spacingInline.xs),
            padding: EdgeInsets.only(left: tokens.spacingInline.xs),
            decoration: BoxDecoration(
              border:
                  Border(left: BorderSide(color: tokens.colors.neutral.light2)),
            ),
            child: SMListItem(
              padding: EdgeInsets.only(top: tokens.spacingInline.xs),
              leading: Text(tokens.textTransform.body(deliveryPrice),
                  style: tokens.typography.typeStyles.body),
              title: title ??
                  Text(
                    tokens.textTransform.body(deliveryTitle),
                    style: tokens.typography.typeStyles.body
                        .copyWith(color: tokens.colors.brand.pure1),
                  ),
              subtitle: subtitle ??
                  Text(
                    tokens.textTransform.body(deliveryTime),
                    style: tokens.typography.typeStyles.bodySm.copyWith(
                      color: tokens.colors.typography.light2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
              trailing: hideTrailing
                  ? null
                  : _LocalRadioButton(
                      isSelected: isSelected,
                      onTap: onSelect,
                    ),
            ),
          ),
        )
      ],
    );
  }
}

class _LocalRadioButton extends StatelessWidget {
  const _LocalRadioButton({
    required this.isSelected,
    required this.onTap,
  });

  final bool isSelected;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    final addressController = context.locateService<AddressController>();
    return SmObserver(builder: (context) {
      return SMRadioButton(
        isChecked: isSelected,
        isDisabled: addressController.isLoading.value ?? false,
        onTap: () => onTap(),
      );
    });
  }
}
