import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_core/modules/auth/models/user_orders/user_orders.dart';
import 'package:soma_core/modules/checkout/models/address_shipping_option_request/address.dart';
import '../../../../soma_ui.dart';

class SMAddressRegistration extends StatefulWidget {
  final String headerText;

  const SMAddressRegistration({
    super.key,
    required this.headerText,
  });

  @override
  State<SMAddressRegistration> createState() => _SMAddressRegistrationState();
}

class _SMAddressRegistrationState extends State<SMAddressRegistration>
    with
        SomaCoreStateMixin,
        DesignTokensStateMixin,
        AppRoutesStateMixin,
        AppAssetsStateMixin,
        AnalyticsEventDispatcherStateMixin {
  var formKey = GlobalKey<FormState>();

  TextEditingController cepInputController = TextEditingController();
  TextEditingController streetInputController = TextEditingController();
  TextEditingController neighborhoodInputController = TextEditingController();
  TextEditingController nameInputController = TextEditingController();
  TextEditingController numberInputController = TextEditingController();
  TextEditingController complementInputController = TextEditingController();

  late RxBool _isCepInvalid;
  late RxString _invalidCepMessage;

  late FocusNode cepFocusNode;

  @override
  void initState() {
    super.initState();
    dispatchLogScreenViewEvent(
      screenClass: 'AddressRegistration',
      screenName: 'Registrar endereço',
    );

    updateInputsWithAddressController();

    if (cepInputController.text.isNullOrEmpty) {
      getCepFromLastPurchase();
    }

    _isCepInvalid = addressController.isCepInvalid;
    _invalidCepMessage = addressController.invalidCepMessage;

    cepFocusNode = FocusNode();

    cepFocusNode.addListener(() {
      if (!cepFocusNode.hasFocus) {
        searchZipCode(cepInputController.text);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    cepFocusNode.dispose();
  }

  Future<void> _checkIfNeedsToUpdatePayment() async {
    final currentSelectedInstallments =
        paymentsController.orderForm.value?.currentInstallmentCountSelected;

    /// A requisição de shippingOptions limpa o parcelamento selecionado previamente
    /// caso o cartão de crédito seja novo, por isso chamamos denovo o método
    /// de seleção de pagamento. Isso se o parcelamento realmente estiver limpo (0 parcelas).
    if (currentSelectedInstallments == 0) {
      await paymentsController.installmentSelect();
    }
  }

  void shippingOptions() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      addressController.addressInfo.value = Address(
        receiverName: nameInputController.text,
        postalCode: cepInputController.text,
        city: addressController.addressInfo.value?.city,
        state: addressController.addressInfo.value?.state,
        street: streetInputController.text,
        number: numberInputController.text,
        neighborhood: neighborhoodInputController.text,
        complement: complementInputController.text,
        country: addressController.addressInfo.value?.country,
        geoCoordinates: [
          addressController.addressInfo.value?.geoCoordinates?[0],
          addressController.addressInfo.value?.geoCoordinates?[1],
        ],
      );

      final selectedInstallment = paymentsController.installment?.value;
      final isNewCreditCardSelected =
          (paymentsController.paymentSelected.value?.cardIsNew ?? false) &&
              selectedInstallment != null;

      await addressController.shippingOptions(cep: cepInputController.text);

      await addressController.updateAddress();

      if (isNewCreditCardSelected) {
        await _checkIfNeedsToUpdatePayment();
      }

      _goToNextPage();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> _goToNextPage() async {
    try {
      final orderForm = addressController.orderForm.value;

      await analyticsController.logChangeAddress(
        orderForm: orderForm,
      );
      if (addressController.selectedDelivery == null) {
        navigateToNamed(shippingRegistrationRoute);
      } else {
        if (paymentsController.paymentSelected.value == null) {
          return navigateToNamed(paymentMethodRoute);
        }
        navigateToNamed(orderReviewRoute);
      }
    } catch (e) {
      navigateToNamed(homeRoute);
    }
  }

  void searchZipCode(cep) async {
    try {
      await addressController.queryCep(
        cep: cepInputController.text,
      );

      setState(() {
        streetInputController.text =
            addressController.addressInfo.value!.street!;

        neighborhoodInputController.text =
            addressController.addressInfo.value!.neighborhood!;
      });

      navigateToNamed(addressRegistrationRoute);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getCepFromLastPurchase() async {
    if (!authController.isLoggedIn) return;
    scheduleMicrotask(() => addressController.isLoading.value = true);
    try {
      UserOrders userOrders = await authController.getUserOrders(pageSize: 5);
      addressController.updateAdressWithFirstOrder(userOrders.list);
      updateInputsWithAddressController();
    } finally {
      addressController.isLoading.value = false;
    }
  }

  updateInputsWithAddressController() {
    cepInputController.text =
        addressController.addressInfo.value?.postalCode ?? "";
    streetInputController.text =
        addressController.addressInfo.value?.street ?? "";
    neighborhoodInputController.text =
        addressController.addressInfo.value?.neighborhood ?? "";
    nameInputController.text =
        '${addressController.orderForm.value?.clientProfileData?.firstName ?? ""} ${addressController.orderForm.value?.clientProfileData?.lastName ?? ""}';
    complementInputController.text =
        addressController.addressInfo.value?.complement ?? "";
    numberInputController.text =
        addressController.addressInfo.value?.number ?? "";
  }

  @override
  Widget build(BuildContext context) {
    final showTitleInNavBar = pagesTheme.showTitleInNavBar;
    final headerTitle = textTransform.title(
        termsAndMessages.textButtonAdressRegistration ?? widget.headerText);
    return SmObserver(
      builder: (context) => Scaffold(
        floatingActionButton: Padding(
          padding: EdgeInsets.symmetric(horizontal: spacingInline.md),
          child: SMButton.primary(
            onPressed: shippingOptions,
            isLoading: addressController.isLoading.value! ||
                checkoutController
                        .paymentsController.isLoadingPaymentSelect.value ==
                    true,
            isDisabled: addressController.isLoading.value!,
            expanded: true,
            child: Text(textTransform.button('continuar')),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        body: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SMNavBar.adaptative(
                  title: showTitleInNavBar ? Text(headerTitle) : null,
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top: spacingStack.xs,
                      left: spacingInline.md,
                      right: spacingInline.md),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SMMainContent(
                        image: addressRegistrationHeaderImage,
                        title: !showTitleInNavBar ? headerTitle : null,
                      ),
                      SizedBox(height: spacingStack.lg),
                      SMCepInput(
                        onChange: (v) {
                          if (v.replaceAll(RegExp(r'\D'), '').length == 8) {
                            searchZipCode(v);
                            FocusScope.of(context).unfocus();
                          }
                        },
                        controller: cepInputController,
                        focusNode: cepFocusNode,
                        suffixWidget: Icon(
                          icons.close,
                          size: 18,
                        ),
                        onSubmit: searchZipCode,
                        action: () => cepInputController.text = '',
                        validator: (_) {
                          if (_isCepInvalid.value) {
                            return _isCepInvalid.value
                                ? _invalidCepMessage.value
                                : null;
                          }

                          if (cepInputController.text.isEmpty) {
                            return cepInputController.text.isEmpty
                                ? 'O cep não pode vir vazio'
                                : null;
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: spacingStack.lg),
                      SMListItem(
                        contentAlignment: CrossAxisAlignment.center,
                        leading: tokens.pickupTheme.bagWithProductsTheme
                                .addressCardTheme.hideIcon
                            ? null
                            : Icon(
                                icons.locationPin,
                                color: colors.neutral.pure2,
                              ),
                        title: Text(
                          addressController.addressInfo.value?.city != null
                              ? "${addressController.addressInfo.value?.city!} - ${addressController.addressInfo.value?.state!}"
                              : "",
                        ),
                      ),
                      SizedBox(height: spacingStack.md),
                      SMInput(
                        strokeColor: tokens.inputTheme.strokeColor,
                        inputController: nameInputController,
                        labelText: 'nome de quem vai receber',
                        inputHeight: InputHeight.large,
                        validator: (value) =>
                            value!.isEmpty ? 'O nome não pode vir vazio' : null,
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        supportText: null,
                      ),
                      SizedBox(height: spacingStack.sm),
                      SMInput(
                        strokeColor: tokens.inputTheme.strokeColor,
                        labelText: 'nome da rua',
                        inputHeight: InputHeight.large,
                        validator: (value) =>
                            value!.isEmpty ? 'A rua não pode vir vazia' : null,
                        inputController: streetInputController,
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        supportText: null,
                      ),
                      SizedBox(height: spacingStack.sm),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SMInput(
                            strokeColor: tokens.inputTheme.strokeColor,
                            inputController: numberInputController,
                            labelText: 'número',
                            inputHeight: InputHeight.medium,
                            validator: (value) => value!.trim().isEmpty
                                ? 'O número não pode vir vazio'
                                : null,
                            inputWidth: 111,
                            autovalidateMode:
                                AutovalidateMode.onUserInteraction,
                            supportText: null,
                          ),
                          const SizedBox(width: 8),
                          SMInput(
                            strokeColor: tokens.inputTheme.strokeColor,
                            labelText: 'bairro',
                            inputHeight: InputHeight.medium,
                            inputWidth: 220,
                            inputController: neighborhoodInputController,
                            validator: (value) => value!.isEmpty
                                ? 'O bairro não pode vir vazio'
                                : null,
                            autovalidateMode:
                                AutovalidateMode.onUserInteraction,
                            supportText: null,
                          ),
                        ],
                      ),
                      SizedBox(height: spacingStack.sm),
                      SMInput(
                        strokeColor: tokens.inputTheme.strokeColor,
                        labelText: 'complemento',
                        inputHeight: InputHeight.large,
                        inputController: complementInputController,
                        supportText: 'opcional',
                        supportTextStyle: typography.typeStyles.bodyCaption
                            .copyWith(color: colors.typography.light2),
                      ),
                      SizedBox(
                          height: 40.0 + buttonHeights[ButtonHeight.large]),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
