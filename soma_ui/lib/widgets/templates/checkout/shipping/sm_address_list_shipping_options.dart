import 'dart:async';

import 'package:flutter/material.dart';

import 'package:collection/collection.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'package:soma_core/modules/checkout/controllers/address_controller.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/pickup_options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/shipping_options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/slas.dart';
import 'package:soma_ui/widgets/templates/checkout/shipping/sm_options_list_tile.dart';
import 'package:soma_ui/widgets/templates/checkout/shipping/sm_pickup_in_point_list_tile_new.dart';
import 'package:soma_ui/widgets/templates/pdp/widgets/sm_pdp_delivery_preview.dart';

typedef OnSelectShippingOption = FutureOr<void> Function(Options option);

typedef OnSelectPickupInPointOption = FutureOr<void> Function({
  required PickupOptions pickupInPoint,
  required PickupStore store,
  required String receiverName,
});

class SmAddressListShippingOptions extends StatefulWidget {
  final ShippingOptionsNew? options;
  final OnSelectShippingOption? onSelectShippingOption;
  final OnSelectPickupInPointOption? onSelectPickupInPointOption;
  final List<ItemDeliveryInfo>? value;
  final bool economicUsesSimpleSubtitle;
  final String? receiverName;
  final bool showOthersStoresOptionInPickup;
  final bool hideTags;
  final bool isBag;

  const SmAddressListShippingOptions({
    super.key,
    this.options,
    this.onSelectShippingOption,
    this.onSelectPickupInPointOption,
    this.economicUsesSimpleSubtitle = true,
    this.value,
    this.receiverName,
    this.showOthersStoresOptionInPickup = true,
    this.hideTags = false,
    this.isBag = false,
  });

  @override
  State<SmAddressListShippingOptions> createState() =>
      _SmAddressListShippingOptionsState();
}

class _SmAddressListShippingOptionsState
    extends State<SmAddressListShippingOptions>
    with DesignTokensStateMixin, SomaCoreStateMixin {
  bool isChangingShippingType = false;

  @override
  Widget build(BuildContext context) {
    if (widget.options != null) {
      return _buildOptions(widget.options);
    }

    return SmObserver(
      builder: (context) {
        return _buildOptions(addressController.shippingOptionsNewData.value);
      },
    );
  }

  void setChangingLoadingValue(bool value) {
    setState(() {
      isChangingShippingType = value;
    });
  }

  Widget _buildOptions(ShippingOptionsNew? shippingOptionsNew) {
    Widget? widget;

    final isDeliverySelected = addressController.shippingType.value.isDelivery;
    final isPickupSelected = addressController.shippingType.value.isPickup;
    final hasPickupPointAndAddess = (addressController.shippingOptionsNewData
                    .value?.pickupInPoint?.pickupPoints ??
                [])
            .isNotEmpty &&
        addressController.addressInfo.value != null;

    if (isDeliverySelected) {
      widget = _buildDeliveryOptions(shippingOptionsNew);
    } else if (isPickupSelected) {
      widget = _buildPickupOptions(shippingOptionsNew);
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget != null) SizedBox(height: spacingStack.xxs),
        if (hasPickupPointAndAddess)
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(tokens.borderRadius.radiusPill),
                border: Border.all(
                  width: 1,
                  color: tokens.colors.neutral.medium1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SMPrimaryButton(
                    onPressed: () async {
                      setChangingLoadingValue(true);
                      await addressController.resetToDeliveryShipping();
                      addressController.shippingType.value =
                          ShippingType.delivery;
                      setChangingLoadingValue(false);
                    },
                    size: ButtonSize.small,
                    style: SMButtonStyle(
                      backgroundColor: isDeliverySelected
                          ? tokens.colors.typography.pure2
                          : Colors.white,
                    ),
                    child: Text(
                      textTransform.title("Receber"),
                      style: TextStyle(
                        color: isDeliverySelected
                            ? tokens.colors.typography.pure1
                            : tokens.colors.typography.pure2,
                      ),
                    ),
                  ),
                  SMPrimaryButton(
                    onPressed: () async {
                      setChangingLoadingValue(true);
                      final store =
                          shippingOptionsNew?.getStoreWithMostProducts();
                      if (store == null) return;
                      await selectStore(store: store);
                      setChangingLoadingValue(false);
                    },
                    size: ButtonSize.small,
                    style: SMButtonStyle(
                      backgroundColor: isPickupSelected
                          ? tokens.colors.typography.pure2
                          : Colors.white,
                    ),
                    child: Text(
                      textTransform.title("Retirar na loja"),
                      style: TextStyle(
                        color: isPickupSelected
                            ? tokens.colors.typography.pure1
                            : tokens.colors.typography.pure2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (widget != null) SizedBox(height: spacingStack.xxs),
        if (widget != null) isChangingShippingType ? const SMSpinner() : widget,
      ].separated(SizedBox(height: spacingStack.sm)),
    );
  }

  Widget _buildDeliveryOptions(
    ShippingOptionsNew? shippingOptionsNew, {
    bool keepPickup = false,
  }) {
    List<Widget> components = [];

    if ((shippingOptionsNew?.deliveryData?.packs ?? []).length > 1) {
      String id = "0";
      String title = "Prazos variados";
      bool isSelected = true;

      String price = addressController.getDeliveryPrice(
        addressController.controllerPackages.value,
      );

      String time = addressController.getDeliveryTime(
        addressController.controllerPackages.value,
      );

      components = [
        SmOptionsListTile(
          id: id,
          deliveryPrice: price,
          deliveryTitle: title,
          deliveryTime: time,
          deliveryTypeAtTop: tokens.bagTheme.deliveryOptionsTypeAtTop,
          receiveText: tokens.bagTheme.deliveryOptionsReceiveText,
          useSimpleSubtitle: false,
          onSelect: () {
            addressController.updateDeliveryOption(
              CommonDeliveryOption(
                id: id,
                deliveryPrice: price,
                deliveryTitle: title,
                deliveryTime: time,
              ),
              keepPickup: keepPickup,
            );
          },
          isSelected: isSelected,
          hideTrailing: false,
          subtitle: null,
        ),
      ];
    } else {
      final packages = addressController.controllerPackages.value?.first;
      if (packages == null) return const SizedBox();

      final deliveryOptionsList = <Map<String, dynamic>>[];

      packages.deliveryOptions.forEachIndexed((index, element) {
        deliveryOptionsList.add({
          "id": index.toString(),
          "price": element.shippingPrice,
          "title": element.name ?? "",
          "time": element.shippingEstimatedFormatted ?? "",
          "isSelected": addressController.deliveryOptionSelected.value?.id ==
              index.toString(),
        });
      });

      deliveryOptionsList.sort((a, b) => b["price"].compareTo(a["price"]));

      components = deliveryOptionsList.map((map) {
        final tokens = context.designTokens;
        final deliveryTheme = tokens.deliveryTheme;
        final optionItem = deliveryTheme.optionItem;

        final customDeliveryName = deliveryTheme.customDeliveryName;

        String id = map["id"];
        String price = map["price"];
        String title = map["title"];
        String time = map["time"];
        bool isSelected = map["isSelected"];
        bool isCustom = title == customDeliveryName;

        Widget? turboTag = ModuleRegistry.root
            .mapPluginsOfType<DeliveryTagPlugin, Widget>(
                (p) => p.build(context, forceTag: true))
            .firstOrNull;

        Widget? specialWidget = ModuleRegistry.root
            .mapPluginsOfType<DeliveryOptionPDPPlugins, Widget>(
                (p) => p.build('bag'))
            .firstOrNull;

        return SmOptionsListTile(
          id: id,
          deliveryPrice: price,
          deliveryTitle: title,
          deliveryTime: time,
          deliveryTypeAtTop: tokens.bagTheme.deliveryOptionsTypeAtTop,
          receiveText: tokens.bagTheme.deliveryOptionsReceiveText,
          useSimpleSubtitle: false,
          isSelected: isSelected,
          onSelect: () {
            addressController.updateDeliveryOption(
              CommonDeliveryOption(
                id: id,
                deliveryPrice: price,
                deliveryTitle: title,
                deliveryTime: time,
              ),
              keepPickup: keepPickup,
            );
          },
          hideTrailing: false,
          subtitle: isCustom ? specialWidget : null,
          title: isCustom
              ? Wrap(
                  runSpacing: tokens.spacingStack.xs,
                  children: [
                    Padding(
                      padding: optionItem?.body.title.padding ??
                          const EdgeInsets.all(0),
                      child: Text(
                        tokens.textTransform.body("receber em casa"),
                        style: optionItem?.body.title.textStyle ??
                            tokens.typography.typeStyles.subtitle,
                      ),
                    ),
                    if (turboTag != null) ...[
                      SizedBox(width: tokens.spacingStack.xs),
                      turboTag,
                    ],
                  ],
                )
              : null,
        );
      }).toList();
    }

    return Column(
      children: components.separated(
        SizedBox(height: spacingStack.sm),
      ),
    );
  }

  Widget _buildPickupOptions(ShippingOptionsNew? shippingOptionsNew) {
    List<Widget> components = [];
    final pickupPoints = shippingOptionsNew?.pickupInPoint?.pickupPoints ?? [];

    if (pickupPoints.isNotEmpty &&
        config.appFeaturesConfig.storePickupConfig?.isEnabled == true) {
      final selectedStoreId = addressController
          .shippingOptionsNewData.value?.pickupInPoint?.selectedSla;

      final storeSelected = selectedStoreId != null
          ? pickupPoints
              .firstWhereOrNull((store) => store.id == selectedStoreId)
          : null;

      components.add(
        SmPickupInPointListTileNew(
          onSelectStore: (
            List<Slas> stores,
            Slas? storeSelected,
            receiverName,
          ) {
            if (storeSelected == null) return;
            selectStore(store: storeSelected, receiverName: receiverName);
          },
          receiverName: widget.receiverName,
          allStores: pickupPoints,
          hideTrailing: true,
          storeSelected: storeSelected,
        ),
      );

      int itemsCountLeft =
          (checkoutController.orderForm.value?.getAddedItems ?? []).length -
              (storeSelected?.itemsId ?? []).length;

      if (itemsCountLeft > 0) {
        final packages = addressController.controllerPackages.value?.first;
        final optionsCount = packages?.deliveryOptions.length ?? 0;

        components.add(
          const SmHorizontalDivider(
            topSpacing: 12,
            bottomSpacing: 12,
            horizontalSpacing: 0,
          ),
        );

        components.add(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "entrega dos demais itens",
                style: tokens.typography.typeStyles.body.copyWith(
                  color: tokens.colors.typography.pure2,
                ),
              ),
              if (itemsCountLeft > 1)
                Text(
                  optionsCount == 1
                      ? "$itemsCountLeft itens não podem ser retirados em loja e serão entregues na forma abaixo"
                      : "$itemsCountLeft itens não podem ser retirados em loja, escolha a forma que deseja recebê-los",
                  style: tokens.typography.typeStyles.bodySm.copyWith(
                    color: tokens.colors.typography.medium2,
                  ),
                ),
              if (itemsCountLeft == 1)
                Text(
                  optionsCount == 1
                      ? "$itemsCountLeft item não pode ser retirado em loja e será entregue na forma abaixo"
                      : "$itemsCountLeft item não pode ser retirado em loja, escolha a forma que deseja recebê-lo",
                  style: tokens.typography.typeStyles.bodySm.copyWith(
                    color: tokens.colors.typography.medium2,
                  ),
                ),
              SizedBox(height: tokens.spacingInline.sm),
            ],
          ),
        );

        components.add(
          _buildDeliveryOptions(
            shippingOptionsNew,
            keepPickup: true,
          ),
        );
      }
    }

    return Column(
      children: components.separated(
        SizedBox(height: spacingStack.sm),
      ),
    );
  }

  Future<void> selectStore({required Slas store, String? receiverName}) async {
    await addressController.setPickupShipping(
      itemIds: store.itemsId ?? [],
      slaDeliveryChannel: store.deliveryChannel ?? "",
      slaId: store.id ?? "",
      receiverName: receiverName,
    );
    addressController.shippingType.value = ShippingType.pickup;
  }
}

class ComplexSubtitle extends StatelessWidget {
  const ComplexSubtitle({super.key, required this.option});

  final Options option;

  @override
  Widget build(BuildContext context) {
    final config = context.locateService<Config>();
    final tokens = context.designTokens;

    ShippingOptionsConfig shippingOptionsConfig =
        config.appFeaturesConfig.shippingOptionsConfig;
    final valuesReader = shippingOptionsConfig.valuesReader;
    final estimateNumberStr = option.shippingEstimateNumber;
    String estimateTime = "";
    final estimateNumber = int.tryParse(
      estimateNumberStr?.replaceAll(RegExp(r'\D'), '') ?? '',
    );
    final isSameDayEstimate = estimateNumber == 0;
    final isCustomDelivery =
        option.packs.contains(tokens.deliveryTheme.customDeliveryName) &&
            option.shippingEstimate.toLowerCase().contains("h");

    if (isSameDayEstimate) {
      estimateTime = isCustomDelivery
          ? "${option.shippingEstimate.split('h')[0]} horas"
          : "${tokens.termsAndMessages.expressDeliverySubtitle}";
    }

    final isOneDayEstimate = estimateNumber == 1;
    estimateTime =
        "${estimateNumber ?? ''} ${isOneDayEstimate ? 'dia útil' : 'dias úteis'}";

    return DefaultTextStyle.merge(
      style: tokens.typography.typeStyles.bodySm.copyWith(
        color: tokens.colors.typography.light2,
        fontWeight: FontWeight.w400,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Em até $estimateTime"),
          SizedBox(height: tokens.spacingStack.xs),
          Text(valuesReader.getLabel(option)),
        ],
      ),
    );
  }
}

abstract class DeliveryTagPlugin extends Plugin {
  String get type;

  Widget? build(
    BuildContext context, {
    bool forceTag,
  });
}

abstract class DeliveryOptionBagPlugins extends Plugin {
  Widget? build(String type);
}
