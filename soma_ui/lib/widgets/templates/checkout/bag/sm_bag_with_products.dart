import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'package:soma_core/modules/checkout/models/index.dart';
import 'package:soma_core/modules/checkout/utils/product_gesture_counter.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/templates/checkout/bag/index.dart';
import 'package:soma_ui/widgets/templates/prismic/index.dart';
import 'package:soma_ui/widgets/templates/checkout/payment/cashback/sm_cashback_purchase_incentive_bag.dart';
import 'package:soma_ui/widgets/templates/checkout/resume/sm_resume.dart';
import 'package:soma_ui/widgets/templates/wishlist_v2/sm_checkout_wishlist_v2.dart';

class SmBagWithProducts extends StatefulWidget {
  const SmBagWithProducts({
    super.key,
    required this.bagButtonClosePurchaseKey,
  });
  final GlobalKey bagButtonClosePurchaseKey;
  @override
  State<SmBagWithProducts> createState() => _SmBagWithProductsState();
}

class _SmBagWithProductsState extends State<SmBagWithProducts>
    with
        SomaCoreStateMixin,
        DesignTokensStateMixin,
        AppRoutesStateMixin,
        AppAssetsStateMixin,
        AnalyticsEventDispatcherStateMixin,
        ProductGestureCounter,
        ShippingPriceChangesNotifier {
  @override
  late final storage = context.locateService<KeyValueStorage>();
  bool get isGiftWrapEnabled =>
      appFeaturesConfig.giftWrapConfig?.isEnabled ?? false;
  List<Body> bodies = [];
  List<ProdutcOrderForm> unavailableItems = [];
  List<ProdutcOrderForm> productsList = [];
  List<Product> suggestedProducts = [];
  final scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _sendAnalytics();
    });

    Future.delayed(Duration.zero, () async {
      await checkoutController.updateOrderForm();
      if (!mounted) {
        return;
      }
      wishlistController.getWishlistCatalogAuthDynamic();

      bool hasCep = addressController.hasPostalCode;
      bool hasSlas = orderForm
              ?.shippingData?.logisticsInfo?.firstOrNull?.slas?.isNotEmpty ??
          false;

      if (hasCep && !hasSlas) {
        await addressController.queryCep(
            cep: addressController.addressInfo.value!.postalCode!);
      }

      _selectShippingOptions();

      unavailableItems =
          checkoutController.orderForm.value!.getUnavailableItems;
      itemsController.toogleSendToGift.value =
          checkoutController.orderForm.value?.hasProductSendToGigt ?? false;
    });

    scheduleMicrotask(() {
      if (appFeaturesConfig.wishlistV2) {
        if (wishlistV2Controller.wishlist.value != null) {
          wishlistV2Controller.getFolderData(
            wishlistV2Controller.wishlist.value!.folders.first.id,
          );
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      int animationCount = getAnimationCount();
      if (animationCount < 3) {
        animateActionButtons();
        saveProductGestureCounter(animationCount + 1);
      }
      await itemsController.removeProductsWithExcessiveDiscount();
      _loadBagItems();
    });
  }

  void gestureSlide(GestureBinding gestureBinding, double x, double y,
      AxisDirection axisDirection) {
    double yInitialPosition = y;
    double xInitialPosition = x;
    PointerEvent addPointer = PointerAddedEvent(
        pointer: 1, position: Offset(xInitialPosition, yInitialPosition));
    PointerEvent downPointer = PointerDownEvent(
        pointer: 1, position: Offset(xInitialPosition, yInitialPosition));
    gestureBinding.handlePointerEvent(addPointer);
    gestureBinding.handlePointerEvent(downPointer);
    double dx = 7;
    double updateCount = dx;
    for (int i = 0; i < dx; i++) {
      PointerEvent movePointer = PointerMoveEvent(
          pointer: 1,
          delta: axisDirection == AxisDirection.right
              ? Offset(-dx, 0)
              : Offset(dx, 0),
          position: axisDirection == AxisDirection.right
              ? Offset(xInitialPosition - i * dx, yInitialPosition)
              : Offset(xInitialPosition + i * dx, yInitialPosition));
      gestureBinding.handlePointerEvent(movePointer);
    }
    PointerEvent upPointer = PointerUpEvent(
        pointer: 1,
        position:
            Offset(xInitialPosition - dx * updateCount, yInitialPosition));
    gestureBinding.handlePointerEvent(upPointer);
  }

  void animateActionButtons() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    gestureSlide(GestureBinding.instance, 122.8, 300.9, AxisDirection.right);
    await Future.delayed(const Duration(milliseconds: 1500));
    gestureSlide(GestureBinding.instance, 300.8, 300.9, AxisDirection.left);
    await Future.delayed(const Duration(milliseconds: 1000));
    gestureSlide(GestureBinding.instance, 300.8, 300.9, AxisDirection.left);
    await Future.delayed(const Duration(milliseconds: 1500));
    gestureSlide(GestureBinding.instance, 122.8, 300.9, AxisDirection.right);
  }

  Future<void> _loadBagItems() async {
    try {
      var bagItems = await catalogController.getBagItems();
      if (mounted) {
        setState(() => bodies = bagItems);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> _selectShippingOptions() async {
    final orderForm = checkoutController.orderForm.value;
    if (orderForm?.messages?.isNotEmpty == true) {
      final hasDeliveryChangesMessages = orderForm?.messages?.any((element) {
            if (element.text is! String) return false;
            return element.text!.contains('O valor do frete foi alterado') ||
                element.text!.contains('O valor dos itens foi alterado');
          }) ??
          false;
      if (!hasDeliveryChangesMessages) return;
    }
    if (orderForm != null &&
        orderForm.items!.isNotEmpty &&
        orderForm.shippingData?.address?.postalCode != null &&
        authController.isLoggedIn) {
      try {
        getCepFromLastPurchase();
      } catch (e, st) {
        debugPrint(e.toString());
        debugPrintStack(stackTrace: st);
      }
    }
  }

  void getCepFromLastPurchase() async {
    if (authController.isLoggedIn || !addressController.hasPostalCode) return;
    var userOrders = await authController.getUserOrders(pageSize: 5);
    addressController.updateAdressWithFirstOrder(userOrders.list);
  }

  void _sendAnalytics() async {
    final currentRoute = currentNavigationRoute;
    final isOnPdp = currentRoute == '/productDetails';

    if (!mounted || isOnPdp) return;
    final orderForm = checkoutController.orderForm.value;

    await dispatchVisitCartPage(
      orderForm: orderForm,
      context: context,
    );

    dispatchLogScreenViewEvent(
      screenClass: 'BagWithProducts',
      screenName: 'Checkout',
    );
    analyticsController.logCheckoutEvent(
      orderForm: orderForm,
      screenName: 'Checkout',
      eventName: 'view_cart',
      screenClass: 'BagWithProducts',
    );
  }

  @override
  Widget build(BuildContext context) {
    final canPop = ModalRoute.of(context)?.canPop ?? false;
    final bagBenefits = bodies
        .where((b) => b.sliceType == PrismicSliceType.pixBenefits.prismicName)
        .firstOrNull;

    final sections = [
      SizedBox(height: bagTheme.initialPadding?.vertical ?? spacingStack.xl),
      SmBagListProducts(
          bagButtonClosePurchaseKey: widget.bagButtonClosePurchaseKey),
      if (appFeaturesConfig.selectGift.isEnabled) ...[
        const SmSelectGift(),
      ],
      Padding(
        padding: EdgeInsets.symmetric(horizontal: spacingInline.md),
        child: Column(
          children: [
            if (isGiftWrapEnabled &&
                (orderForm != null &&
                    orderForm!.getItemsSendToGift.isNotEmpty)) ...[
              SizedBox(height: spacingStack.lg),
              const SmBagSendToGift(),
              const SmHorizontalDivider(horizontalSpacing: 0, bottomSpacing: 0),
            ],
            if (!appFeaturesConfig.hideTitlesBagCoupons)
              SizedBox(height: spacingStack.xl)
            else
              SizedBox(height: spacingStack.md),
            const SmBagDelivery(),
            _BagBenefitsBanner(bagBenefits),
            if (!appFeaturesConfig.hideTitlesBagCoupons)
              SizedBox(height: spacingStack.xl),
            if (appFeaturesConfig.useCouponPlugin == false) ...[
              const SmBagAddCoupons(),
            ],
            if (appFeaturesConfig.cashBackConfig?.isEnableTagBag == true) ...[
              SizedBox(height: spacingStack.sm),
              const SMCashbackPurchaseIncentiveBag()
            ]
          ],
        ),
      ),
      SizedBox(height: spacingStack.md),
      const SMResume(isBag: true),
      SizedBox(height: spacingStack.md),
      bagTheme.hasWishlist == true
          ? appFeaturesConfig.wishlistV2
              ? const SMCheckoutWishlistV2()
              : const SMCheckoutWishlist()
          : const SizedBox.shrink(),
      SizedBox(height: spacingStack.xl),
      SMCmsContent.forDocument(
        'bag',
        shrinkWrap: true,
        scrollController: scrollController,
      ),
    ];
    final bagSectionsPlugins =
        ModuleRegistry.root.pluginsOfType<BagSectionPlugin>();
    for (final plugin in bagSectionsPlugins) {
      try {
        final widget = plugin.build(context);
        if (widget != null) {
          sections.insert(plugin.index, widget);
        }
      } catch (e, st) {
        debugPrint(e.toString());
        debugPrintStack(stackTrace: st);
        analyticsController.logError(
          e,
          st,
          information: [
            'Erro ao executar plugin ${plugin.runtimeType}',
          ],
        );
      }
    }

    return SmObserver(builder: (context) {
      return Column(
        children: [
          bagTheme.hasNavBar == true
              ? SMNavBar.adaptative(
                  leading: canPop
                      ? SmCloseButton(
                          onTap: () {
                            final tabItems = config.tabItems;
                            var tabIndex = tabItems.getIndexByAlias('home');
                            if (previousNavigationRoute == '/orderReview') {
                              tabIndex = tabItems.getIndexByAlias('bag');
                            } else if (previousNavigationRoute ==
                                '/productDetails') {
                              return navigateBack();
                            }
                            if (tabIndex != null) {
                              context
                                  .locateService<SomaUIController>()
                                  .selectedBottomNavigationIndex(tabIndex);
                            }
                            ModuleRegistry.root
                                .forEachPluginOfType<DisconnectTurboModePlugin>(
                              (p) => p.disconnectTurbo(),
                            );
                            navigateToNamedAndRemoveAll(homeRoute);
                          },
                        )
                      : null,
                  title: Text(textTransform.title(termsAndMessages.bag)),
                  trailing: Text(
                    checkoutController.getCartItemsQuantity() == 1
                        ? '${checkoutController.getCartItemsQuantity()} produto'
                        : '${checkoutController.getCartItemsQuantity()} produtos',
                  ),
                )
              : SizedBox(
                  height: spacingStack.sm,
                ),
          if (checkoutController
                  .orderForm.value?.getUnavailableItems.isNotEmpty ??
              false)
            const SmBagUnavailableItems(),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                ...sections,
                if (PlatformUtils.isIOS)
                  SizedBox(height: MediaQuery.of(context).padding.bottom),
                SizedBox(height: spacingStack.xxul),
              ],
            ),
          )
        ],
      );
    });
  }
}

class _BagBenefitsBanner extends StatelessWidget {
  const _BagBenefitsBanner(this.body);

  final Body? body;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final imageUrl = body?.primary?.image?.url;
    return imageUrl != null
        ? Column(
            children: [
              SizedBox(height: tokens.spacingStack.md),
              SMCachedNetworkingImage(imageUrl: imageUrl),
            ],
          )
        : const SizedBox.shrink();
  }
}

abstract class DisconnectTurboModePlugin implements Plugin {
  void disconnectTurbo();
}
