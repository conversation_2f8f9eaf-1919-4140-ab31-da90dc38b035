import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:soma_ui/widgets/templates/checkout/shipping/bottom_sheet/sm_shipping_bottom_sheet.dart';
import '../../../../soma_ui.dart';
import '../shipping/sm_address_list_shipping_options.dart';
import 'package:soma_core/modules/checkout/models/address_shipping_option_request/address.dart';

class SmBagDelivery extends StatefulWidget {
  const SmBagDelivery({super.key});

  @override
  State<SmBagDelivery> createState() => _SmBagDeliveryState();
}

class _SmBagDeliveryState extends State<SmBagDelivery>
    with
        DesignTokensStateMixin,
        AppRoutesStateMixin,
        AnalyticsEventDispatcherStateMixin,
        SomaCoreStateMixin {
  Rxn<Address> get addressInfo => addressController.addressInfo;

  @override
  Widget build(BuildContext context) {
    return SmObserver(
      builder: (context) {
        final hasAddress = addressInfo.value != null;

        final fixedTitle = bagTheme.deliveryFixedTitle;

        final title = hasAddress && fixedTitle == false
            ? 'entrega para ${addressInfo.value?.city} - ${addressInfo.value?.state}'
            : 'frete e prazo de entrega';

        String? subtitle = termsAndMessages.bagDeliverySubtitle;

        if (hasAddress) {
          subtitle =
              '${textTransform.body('CEP')} ${MaskTextInputFormatter(mask: '#####-###').maskText(addressInfo.value?.postalCode ?? "")}';
        }

        if (hasAddress && bagTheme.deliveryExtendedSubtitle) {
          subtitle =
              '${MaskTextInputFormatter(mask: '#####-###').maskText(addressInfo.value?.postalCode ?? "")} - ${addressInfo.value?.city ?? ""} - ${addressInfo.value?.state ?? ""}';
        }

        final hideBagIcons =
            Theme.of(context).extension<BagIconTheme>()?.hideBagIcons;

        final withAddressLinkText =
            termsAndMessages.changeAddressLinkTextBag ?? 'alterar';
        final withoutAddressLinkText =
            termsAndMessages.addressLinkTextBag ?? withAddressLinkText;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (appFeaturesConfig.showBagHeaderCep && hasAddress) ...[
              Padding(
                padding: EdgeInsets.only(bottom: tokens.spacingInline.xs),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      textTransform.title('Consultar entrega para'),
                      style: tokens.typography.typeStyles.bodySm,
                    ),
                    Text(
                      textTransform.body(subtitle ?? ""),
                      style: tokens.typography.typeStyles.bodyXs
                          ?.copyWith(color: tokens.colors.neutral.dark2),
                    ),
                  ],
                ),
              )
            ],
            SmSensitiveData(
              child: SMListItem(
                leading:
                    hideBagIcons == true ? null : Icon(icons.motorcycleFast),
                title: !appFeaturesConfig.hideTitlesBagCoupons
                    ? Text(
                        textTransform.body(title),
                        style: bagTheme.deliveryLargeTitle
                            ? tokens.typography.typeStyles.bodyLg
                            : null,
                      )
                    : null,
                subtitle: !appFeaturesConfig.hideTitlesBagCoupons
                    ? subtitle.mapNotEmpty(
                        (subtitle) => Text(
                          subtitle,
                        ),
                      )
                    : null,
                bottom: SMButton.link(
                  onPressed: () {
                    dispatchSelectContentEvent(
                      'alterar:frete-e-prazo-de-entrega',
                      produtcOrderFormList: orderForm?.items,
                    );

                    if (bagTheme.useShippingBottomSheet) {
                      customShowBottomSheet(const SmShippingBottomSheet());
                    } else {
                      navigateToNamed(
                        shippingRegistrationRoute,
                        arguments: "isBagCheckout",
                      );
                    }
                  },
                  child: Padding(
                    padding: appFeaturesConfig.hideTitlesBagCoupons
                        ? EdgeInsets.only(bottom: spacingStack.xxs)
                        : EdgeInsets.zero,
                    child: Text(
                      textTransform.body(hasAddress
                          ? withAddressLinkText
                          : withoutAddressLinkText),
                      style: tokens.bagTheme.buttonLinkStyle?.copyWith(
                            fontWeight:
                                hasAddress ? FontWeight.w400 : FontWeight.w300,
                          ) ??
                          tokens.typography.typeStyles.bodySm.copyWith(
                            fontWeight:
                                hasAddress ? FontWeight.w400 : FontWeight.w300,
                          ),
                    ),
                  ),
                  trailing: Padding(
                    padding: appFeaturesConfig.hideTitlesBagCoupons
                        ? EdgeInsets.only(bottom: spacingStack.xxs)
                        : EdgeInsets.zero,
                    child: Icon(
                      icons.arrowRight,
                    ),
                  ),
                ),
              ),
            ),
            const SmAddressListShippingOptions(isBag: true),
            SizedBox(
                height: appFeaturesConfig.hideTitlesBagCoupons
                    ? spacingInline.md
                    : spacingInline.lg),
            SMLine(
              lineColor: colors.neutral.medium1,
              lineSize: LineSize.small,
            ),
          ],
        );
      },
    );
  }
}
