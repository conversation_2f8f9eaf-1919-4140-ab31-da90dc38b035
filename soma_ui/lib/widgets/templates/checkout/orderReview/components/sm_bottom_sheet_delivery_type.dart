import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:soma_core/modules/checkout/models/index.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/pickup_options.dart';
import 'package:soma_core/modules/checkout/models/shipping_options/shipping_options.dart';
import 'package:soma_core/modules/checkout/services/address_service.dart';
import 'package:soma_core/shared/models/item_delivery_info.dart';
import 'package:soma_core/shared/models/pickup_store.dart';

import '../../../../../soma_ui.dart';
import '../../shipping/sm_address_list_shipping_options.dart';

class SMBottomSheetDeliveryType extends StatefulWidget {
  final List<ItemDeliveryInfo> initialSelectedSlas;
  final OnSelectShippingOption onSelectShippingOption;
  final OnSelectPickupInPointOption onSelectPickupInPointOption;

  const SMBottomSheetDeliveryType({
    super.key,
    required this.initialSelectedSlas,
    required this.onSelectPickupInPointOption,
    required this.onSelectShippingOption,
  });

  @override
  State<SMBottomSheetDeliveryType> createState() =>
      _SMBottomSheetDeliveryTypeState();
}

class _SMBottomSheetDeliveryTypeState extends State<SMBottomSheetDeliveryType>
    with DesignTokensStateMixin, SomaCoreStateMixin {
  late final _shippingOptionsNewFuture = _getShippingOptionsNew();
  SelectedDeliveryOptions? _selectedDeliveryOptions;
  late List<ItemDeliveryInfo> _selectedSlas;

  @override
  void initState() {
    super.initState();
    _selectedSlas = widget.initialSelectedSlas;
  }

  Future<ShippingOptionsNew?> _getShippingOptionsNew() async {
    final orderForm = checkoutController.orderForm.value;
    final bagItems = orderForm?.items;
    final products = widget.initialSelectedSlas
        .map((s) => bagItems?.firstWhereOrNull((i) => i.id == s.itemId))
        .whereType<ProdutcOrderForm>()
        .where((p) => p.id != null && p.seller != null)
        .map((p) => SimulationProducts(id: p.id!, seller: p.seller!))
        .toList();
    final postalCode =
        orderForm?.shippingData?.selectedAddresses?.firstOrNull?.postalCode;
    if (postalCode == null) {
      return null;
    }

    await Future.delayed(Duration.zero);
    return addressController.oldSimulateShippingOptions(
      postalCode: postalCode,
      products: products,
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectedDeliveryOptions = _selectedDeliveryOptions;
    final receiverName =
        selectedDeliveryOptions is PickupSelectedDeliveryOptions
            ? selectedDeliveryOptions.receiverName
            : null;

    return SmObserver(
      builder: (context) => SMBottomSheet(
        onTapClose: () => Navigator.pop(context),
        title: textTransform.title('como você quer receber?'),
        titleStyle: typography.typeStyles.headlineSm,
        buttonTitle: 'confirmar',
        onTap: _onConfirm,
        buttonDisabled: addressController.isLoading.value!,
        isLoading: addressController.isLoading.value!,
        child: FutureBuilder<ShippingOptionsNew?>(
            future: _shippingOptionsNewFuture,
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return kDebugMode
                    ? Text(snapshot.error.toString())
                    : Text(textTransform
                        .body('Não foi possível encontrar opções de frete'));
              }

              if (snapshot.connectionState == ConnectionState.waiting ||
                  !snapshot.hasData ||
                  snapshot.hasError) {
                return const SizedBox.shrink();
              }

              final shippingOptions = snapshot.data!;
              final hasSelectedMultipleGifts =
                  orderForm?.getAddedItemsGifts != null &&
                      orderForm!.getAddedItemsGifts.length > 1;
              final showControllerShippingOptions = hasSelectedMultipleGifts ||
                  appFeaturesConfig.useControllerShippingOptions;

              return SingleChildScrollView(
                child: SmAddressListShippingOptions(
                  options: showControllerShippingOptions
                      ? addressController.shippingOptionsNewData.value
                      : shippingOptions,
                  value: _selectedSlas,
                  economicUsesSimpleSubtitle: false,
                  receiverName: receiverName,
                  onSelectShippingOption: _onSelectShippingOption,
                  onSelectPickupInPointOption: _onSelectPickupInPointOption,
                ),
              );
            }),
      ),
    );
  }

  void _onSelectShippingOption(Options options) {
    setState(() {
      _selectedDeliveryOptions = ShippingSelectedDeliveryOptions(
        options: options,
      );
      _selectedSlas =
          addressController.joinDeliveryInfo(_selectedSlas, options.slas);
    });
  }

  void _onSelectPickupInPointOption({
    required PickupOptions pickupInPoint,
    required String receiverName,
    required PickupStore store,
  }) {
    setState(() {
      _selectedDeliveryOptions = PickupSelectedDeliveryOptions(
        pickupInPoint: pickupInPoint,
        receiverName: receiverName,
        store: store,
      );
      final sameStoreSlas =
          pickupInPoint.slas.where((s) => s.slas?.id == store.id).toList();
      _selectedSlas =
          addressController.joinDeliveryInfo(_selectedSlas, sameStoreSlas);
    });
  }

  void _onConfirm() {
    final selectedDeliveryOptions = _selectedDeliveryOptions;
    if (selectedDeliveryOptions is ShippingSelectedDeliveryOptions) {
      widget.onSelectShippingOption(selectedDeliveryOptions.options);
    } else if (selectedDeliveryOptions is PickupSelectedDeliveryOptions) {
      widget.onSelectPickupInPointOption(
        pickupInPoint: selectedDeliveryOptions.pickupInPoint,
        receiverName: selectedDeliveryOptions.receiverName,
        store: selectedDeliveryOptions.store,
      );
    }
    navigateBack();
  }
}

class SelectedDeliveryOptions {}

class ShippingSelectedDeliveryOptions extends SelectedDeliveryOptions {
  final Options options;

  ShippingSelectedDeliveryOptions({required this.options});
}

class PickupSelectedDeliveryOptions extends SelectedDeliveryOptions {
  final PickupOptions pickupInPoint;
  final String receiverName;
  final PickupStore store;

  PickupSelectedDeliveryOptions({
    required this.pickupInPoint,
    required this.receiverName,
    required this.store,
  });
}
