import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_core/modules/checkout/models/apple_pay/apple_pay_model.dart';
import 'package:adyen_checkout/adyen_checkout.dart';
import 'package:soma_ui/widgets/templates/pdp/combo_steps/utils/combo_navigation_arguments.dart';
// import 'package:soma_ui/widgets/templates/checkout/payment/new_credit_card/sm_new_credit_card_controller.dart';

class SmOrderReviewButtonPurchase extends StatefulWidget {
  const SmOrderReviewButtonPurchase({super.key, this.closeTitle});

  final String? closeTitle;

  @override
  State<SmOrderReviewButtonPurchase> createState() =>
      _SmOrderReviewButtonPurchaseState();
}

class _SmOrderReviewButtonPurchaseState
    extends State<SmOrderReviewButtonPurchase>
    with
        SomaCoreStateMixin,
        DesignTokensStateMixin,
        AppRoutesStateMixin,
        AnalyticsEventDispatcherStateMixin {
  Future<void> _paymentPix() async {
    try {
      await paymentsController.paymentPix();

      navigateToNamed(
        routes.webview,
        arguments: WebViewParams(
            screenClass: 'Web View - Checkout',
            screenName: 'Web View - Checkout',
            url: paymentsController.urlPix,
            showNavBar: false,
            avoidKeyboard: false,
            onUpdateVisitedHistory: (
              controller,
              uri,
            ) {
              if (uri?.path == "/pix-finish") {
                Future.delayed(const Duration(milliseconds: 500), () {
                  controller.goBack();
                  dispatchLogPurchaseEvent(
                    screenClass: 'OrderReviewButtonPurchase',
                    orderForm: orderForm!,
                    transactionId: paymentsController.orderGroup,
                    paymentType: paymentsController
                        .paymentSelected.value?.paymentMethod
                        .toString(),
                  );
                  navigateToNamedAndRemoveAll(
                    confirmationOrderRoute,
                  );
                });
              }
              if (uri?.path == "/pix-fail" || uri?.path == "/cancel") {
                Future.delayed(const Duration(milliseconds: 500), () {
                  controller.goBack();
                  navigateBack();
                });
              }
            }),
      );
    } on SomaCoreExceptionPaymentPixInvalid catch (e) {
      showSnackBar(
        SMSnackBarGetX(
          errorMessage: true,
          designTokens: tokens,
          textLabel:
              'aguarde 2 minutos antes de tentar finalizar o pedido novamente',
          textColor: colors.typography.pure1,
        ),
      );
    } catch (e, stacktrace) {
      debugPrint(e.toString());
      final String message;
      if (e is SomaCoreException && e.cause is RecaptchaException) {
        final cause = e.cause as RecaptchaException;
        message = cause.userFriendlyMessage;
      } else {
        message = 'ocorreu um erro inesperado tente novamente mais tarde';
      }
      showSnackBar(SMSnackBarGetX(
        errorMessage: true,
        designTokens: tokens,
        textLabel: message,
        textColor: colors.typography.pure1,
      ));

      analyticsController.logError(
        e.toString(),
        stacktrace,
        fatal: true,
        orderForm: orderForm?.toJson(),
      );
    }
  }

  Future<ApplePayModel?> _paymentApplePay() async {
    try {
      return await paymentsController.paymentApplePay();
    } catch (e, stacktrace) {
      debugPrint(e.toString());
      final String message;
      if (e is SomaCoreException && e.cause is RecaptchaException) {
        final cause = e.cause as RecaptchaException;
        message = cause.userFriendlyMessage;
      } else if (e is SomaCoreException &&
          (e.message?.contains('CHK0328') ?? false)) {
        message = "Aguarde 2 minutos para tentar novamente";
      } else {
        message = 'ocorreu um erro inesperado tente novamente mais tarde';
      }
      showSnackBar(SMSnackBarGetX(
        errorMessage: true,
        designTokens: tokens,
        textLabel: message,
        textColor: colors.typography.pure1,
      ));
      analyticsController.logError(
        e.toString(),
        stacktrace,
        fatal: true,
        orderForm: orderForm?.toJson(),
      );
    }
    return null;
  }

  Future<void> _paymentCreditCard() async {
    try {
      await paymentsController.paymentCreditCard();
      dispatchLogPurchaseEvent(
        screenClass: 'OrderReviewButtonPurchase',
        orderForm: orderForm!,
        transactionId: paymentsController.orderGroup,
        paymentType:
            paymentsController.paymentSelected.value?.paymentMethod.toString(),
      );

      navigateToNamedAndRemoveAll(confirmationOrderRoute);
    } catch (e, stacktrace) {
      debugPrint(e.toString());
      final String message;
      if (e is SomaCoreException && e.cause is RecaptchaException) {
        final cause = e.cause as RecaptchaException;
        message = cause.userFriendlyMessage;
      } else {
        message = termsAndMessages.failedPaymentMessage ?? e.toString();
      }
      showSnackBar(
        SMSnackBarGetX(
          duration: 10,
          errorMessage: true,
          designTokens: tokens,
          textLabel: message,
          textColor:
              checkoutResumeTheme.colorMessageErrorWithPaymentCreditCard ??
                  colors.typography.pure1,
          position: SnackPosition.TOP,
          backgroundColor: checkoutResumeTheme
                  .backgrondColorMessageErrorWithPaymentCreditCard ??
              tokens.colors.feedback.pureError,
          ignoreTextTransform: true,
        ),
      );
      analyticsController.logError(
        e.toString(),
        stacktrace,
        fatal: true,
        orderForm: orderForm?.toJson(),
      );
      paymentsController.hasErrorWithPayment(true);

      if (onPaymentErrorBehavior ==
              OnPaymentErrorBehavior.redirectToPaymentMethod &&
          mounted) {
        // try {
        //   var newCardController =
        //       context.locateService<SMNewCreditCardController>();
        //   newCardController.currentStep.value =
        //       CreditCardWorkflowStep.cardNumber;
        // } catch (_) {}

        //   // TODO: Com a task VERTAPP-2130, esse redirecionamento foi removido. Futuramente,
        //   // deve ser implementado a abertura do modal com a fluxo da edição/adição de um novo cartão de crédito. Descrito na task.
        //   // navigateToNamed(paymentMethodRoute, arguments: {
        //   //   'pageTitle': termsAndMessages.failedPaymentPageTitle ?? ''
        //   // });
      }
    }
  }

  String get _buttonTitle {
    String? checkingAccountPaymentTitle;
    final isUsingGiftCard = orderFormUtils.getGiftCardValues(orderForm!) > 0;

    ModuleRegistry.root.forEachPluginOfType<PaymentOptionsPlugin>((p) {
      if (orderFormUtils.getGiftCardValues(orderForm!) > 0) {
        checkingAccountPaymentTitle = p.referenceTerm;
      }
    });

    switch (paymentsController.paymentSelected.value?.paymentMethod) {
      case PaymentMethodOptions.pix:
        if (checkingAccountPaymentTitle != null) {
          return 'pagar com $checkingAccountPaymentTitle + pix • $totalOrderValue';
        }

        if (isUsingGiftCard &&
            termsAndMessages.giftCardPaymentOptionTitle != null) {
          return '${termsAndMessages.giftCardPaymentOptionTitle} + pix • $totalOrderValue';
        }

        return 'pagar com pix • $totalOrderValue';
      case PaymentMethodOptions.creditCard:
        if (checkingAccountPaymentTitle != null) {
          return 'pagar com $checkingAccountPaymentTitle + cartão • $totalOrderValue';
        }

        if (isUsingGiftCard &&
            termsAndMessages.giftCardPaymentOptionTitle != null) {
          return '${termsAndMessages.giftCardPaymentOptionTitle} + cartão • $totalOrderValue';
        }
        return 'pagar com cartão • $totalOrderValue';

      case PaymentMethodOptions.giftCard:
        return checkingAccountPaymentTitle != null
            ? 'pagar com $checkingAccountPaymentTitle • $totalOrderValue'
            : '${termsAndMessages.giftCardPaymentOptionTitle} • $totalOrderValue';
      case PaymentMethodOptions.pixInstallments:
        if (isUsingGiftCard &&
            termsAndMessages.giftCardPaymentOptionTitle != null) {
          return '${termsAndMessages.giftCardPaymentOptionTitle} + Pix Parcelado • $totalOrderValue';
        }
        if (checkingAccountPaymentTitle != null) {
          return 'pagar com $checkingAccountPaymentTitle + Pix Parcelado • $totalOrderValue';
        }

        return 'pagar com Pix Parcelado • $totalOrderValue';
      case PaymentMethodOptions.applePay:
        return 'pagar • $totalOrderValue';
      default:
        return 'finalizar pedido';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SmObserver(
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          right: spacingInline.md,
          left: spacingInline.md,
        ),
        child: SMButton.primary(
          expanded: true,
          onPressed: _confirmPurchase,
          isDisabled: paymentsController.isLoading.value! ||
              !paymentsController.selectedPaymentMethodIsAccepted() ||
              paymentsController.hasErrorWithPayment.value!,
          isLoading: paymentsController.isLoading.value! ||
              addressController.isLoading.value!,
          child: Text(
            textTransform.button(_buttonTitle),
            style: orderReviewTheme
                .finalizeButtonTheme?.buttonFinalizeOrderTitleStyle,
          ),
        ),
      ),
    );
  }

  String get totalOrderValue {
    return orderForm.mapNullable(orderFormUtils.getTotalFormatted) ?? '';
  }

  void _confirmPurchase() async {
    if (orderForm?.shippingData?.address?.isComplete == false) {
      showSnackBar(SMSnackBarGetX(
        errorMessage: true,
        designTokens: tokens,
        textLabel: 'Endereço incompleto',
      ));

      return navigateToNamed(addressRegistrationRoute);
    }

    if (!paymentsController.selectedPaymentMethodIsAccepted()) {
      return;
    }
    final selectedPayment = paymentsController.paymentSelected.value;

    if (selectedPayment?.paymentMethod == PaymentMethodOptions.pix ||
        selectedPayment?.paymentMethod ==
            PaymentMethodOptions.pixInstallments) {
      await _paymentPix();
    } else if (selectedPayment?.paymentMethod ==
        PaymentMethodOptions.giftCard) {
      await _paymentCreditCard();
    } else if (selectedPayment?.paymentMethod ==
        PaymentMethodOptions.applePay) {
      final ApplePayModel? applePay = await _paymentApplePay();
      paymentsController.applePayModel?.value = applePay;
      if (applePay != null) {
        customShowBottomSheet(
          SMBottomSheet(
            hideButton: true,
            child: _buildAdyenApplePayAdvancedComponent(applePay),
          ),
        );
      }
    } else {
      if (selectedPayment!.cardIsNew!) {
        await _paymentCreditCard();
      } else {
        customShowBottomSheet(
          SMBottomSheetModalCvvConfirmation(
            onTap: _paymentCreditCard,
          ),
        );
      }
    }
  }

  ApplePayConfiguration _applePayConfiguration(
      ApplePayModel? applePay, ApplePayConfig? config) {
    return ApplePayConfiguration(
      merchantId: config?.merchantId ?? '',
      merchantName: config?.merchantName ?? '',
      merchantCapability: ApplePayMerchantCapability.credit,
      allowOnboarding: true,
      applePaySummaryItems: [
        ///TODO: Implementa config de nome da marca.
        ApplePaySummaryItem(
          label:
              "${Brand.animale.userFriendlyName} em 1x de ${CurrencyUtils.format(
            amount: applePay?.totalCartValue ?? 0,
          )}",
          amount: Amount(
            value: applePay?.amount?.value ?? 0,
            currency: applePay?.amount?.currency ?? "BRL",
          ),
          type: ApplePaySummaryItemType.definite,
        ),
      ],
      requiredShippingContactFields: [
        ApplePayContactField.name,
      ],
    );
  }

  Widget _buildAdyenApplePayAdvancedComponent(ApplePayModel? applePay) {
    final config = context.locateService<Config>();
    paymentsController.applePayConfig.value = config.applePayConfig;
    paymentsController.applePayModel?.value = applePay;
    final ApplePayComponentConfiguration applePayComponentConfiguration =
        ApplePayComponentConfiguration(
      environment: Environment.europe,
      clientKey: applePay?.clientKey ?? '',
      countryCode: applePay?.countryCodeISO2 ?? '',
      amount: Amount(
        value: applePay?.amount?.value ?? 0,
        currency: applePay?.amount?.currency ?? "BRL",
      ),
      shopperLocale: 'pt-BR',
      applePayConfiguration:
          _applePayConfiguration(applePay, config.applePayConfig),
    );
    final AdvancedCheckout advancedCheckout = AdvancedCheckout(
        onSubmit: paymentsController.onSubmit,
        onAdditionalDetails: paymentsController.onAdditionalDetailsMock);
    return AdyenApplePayComponent(
        configuration: applePayComponentConfiguration,
        paymentMethod: {
          "brands": applePay?.paymentMethods?.first.brands,
          "configuration": applePay?.paymentMethods?.first.configuration,
          "name": applePay?.paymentMethods?.first.name,
          "type": applePay?.paymentMethods?.first.type,
        },
        checkout: advancedCheckout,
        loadingIndicator: const SMSpinner(),
        width: 150,
        height: 50,
        onPaymentResult: (paymentResult) {
          if (paymentResult is PaymentAdvancedFinished) {
            if (paymentResult.resultCode.name == 'Authorised') {
              navigateToNamedAndRemoveAll(confirmationOrderRoute);
            }
          }
          if (paymentResult is PaymentCancelledByUser) {
            Navigator.pop(context);
            DialogBuilder.showPaymentResultDialog(paymentResult, context);
            paymentsController.userCancelApplePay(applePay?.paymentId ?? '');
          }
        });
  }
}
