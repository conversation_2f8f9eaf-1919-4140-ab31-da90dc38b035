import 'package:flutter/cupertino.dart';
import 'package:soma_ui/soma_ui.dart';

class PickerData {
  final String label;
  final String? textSuport;
  const PickerData({required this.label, this.textSuport});
}

class SMPicker extends StatefulWidget {
  final List<PickerData> data;
  final void Function(int itemIndex) onSelectedItemChanged;
  final void Function(String value)? onSelectedItemChangedValue;
  final void Function()? onInitCallback;
  final int? initialItem;
  final Color? borderColor;
  final double itemExtent;
  final TextStyle? labelStyle;
  final double? height;
  final Color? backgroundColor;
  final FixedExtentScrollController? scrollController;

  const SMPicker({
    super.key,
    required this.data,
    required this.onSelectedItemChanged,
    this.initialItem = 0,
    this.borderColor,
    this.itemExtent = 48.0,
    this.labelStyle,
    this.height,
    this.backgroundColor,
    this.onSelectedItemChangedValue,
    this.onInitCallback,
    this.scrollController,
  });

  @override
  State<SMPicker> createState() => _SMPickerState();
}

class _SMPickerState extends State<SMPicker>
    with DesignTokensStateMixin, SomaCoreStateMixin {
  static const _diameterRatio = 4.5;
  late final FixedExtentScrollController scrollController;
  late final int initialItem;

  @override
  void initState() {
    super.initState();

    initialItem = widget.initialItem!;

    scrollController = widget.scrollController ??
        FixedExtentScrollController(initialItem: initialItem);

    widget.onInitCallback?.call();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = widget.borderColor ?? colors.neutral.medium1;
    final labelStyle = widget.labelStyle ?? typography.typeStyles.subtitle;
    final height = widget.height ?? 300.0;
    final backgroundColor = widget.backgroundColor ?? colors.typography.pure1;

    List<PickerData> items = widget.data;
    if (config.intelligentSearchConfig.sizeOrder != null) {
      items = sortItems(widget.data, config.intelligentSearchConfig.sizeOrder!);
    }

    return SizedBox(
      height: height,
      child: GestureDetector(
        onTapUp: _selectTappedItem,
        child: CupertinoPicker(
          backgroundColor: backgroundColor,
          selectionOverlay: Container(
            decoration: BoxDecoration(
              border: Border.symmetric(
                horizontal: BorderSide(width: 1.0, color: borderColor),
              ),
            ),
          ),
          itemExtent: widget.itemExtent,
          squeeze: 1.0,
          diameterRatio: _diameterRatio,
          scrollController: scrollController,
          children: [
            ...items.map(
              (item) => Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: item.textSuport != null ? 101 : 0,
                      ),
                      child: Text(
                        textTransform.itemSizePicker(item.label),
                        overflow: TextOverflow.ellipsis,
                        style: labelStyle,
                      ),
                    ),
                  ),
                  if (item.textSuport != null) ...[
                    Center(
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: spacingInline.sm,
                        ),
                        child: SizedBox(
                          width: 90.0,
                          child: Text(
                            textTransform.body(item.textSuport!),
                            style:
                                tokens.smPickerTheme.shouldUseSmallFont == true
                                    ? typography.typeStyles.bodyCaption
                                    : typography.typeStyles.bodySm,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    )
                  ]
                ],
              ),
            )
          ],
          onSelectedItemChanged: (value) {
            widget.onSelectedItemChanged(value);
            if (widget.onSelectedItemChangedValue != null) {
              widget.onSelectedItemChangedValue!(items[value].label);
            }
          },
        ),
      ),
    );
  }

  void _selectTappedItem(TapUpDetails details) {
    // Atualmente o CupertinoPicker não suporta seleção de itens por toque,
    // issue falando sobre: https://github.com/flutter/flutter/issues/24089.
    // A solução utilizada foi copiada desse comentário na mesma issue:
    // https://github.com/flutter/flutter/issues/24089#issuecomment-857630497
    final selectedItem = _findSelectedItem(details, initialItem);
    scrollController.animateToItem(
      selectedItem,
      duration: const Duration(milliseconds: 300),
      curve: Curves.decelerate,
    );
  }

  int _findSelectedItem(TapUpDetails details, int initialItem) {
    final offsetFromTop =
        ((_diameterRatio * widget.itemExtent / 2) / widget.itemExtent).floor();
    final selectedOffset =
        (details.localPosition.dy / widget.itemExtent).ceil();
    final item = scrollController.selectedItem -
        offsetFromTop +
        selectedOffset -
        initialItem -
        1;
    return item;
  }

  List<PickerData> sortItems(
    List<PickerData> items,
    List<String> defaultBrandSizes,
  ) {
    final matchingItems =
        items.where((item) => defaultBrandSizes.contains(item.label)).toList();
    matchingItems.sort((a, b) => defaultBrandSizes
        .indexOf(a.label)
        .compareTo(defaultBrandSizes.indexOf(b.label)));

    final nonMatchingItems = items
        .where((item) => defaultBrandSizes.contains(item.label) == false)
        .toList();
    nonMatchingItems.sort((a, b) => a.label.compareTo(b.label));

    final sortedItems = [...nonMatchingItems, ...matchingItems];
    final indexRn = sortedItems.indexWhere((element) => element.label == "RN");

    if (indexRn != -1) {
      final filterRN = sortedItems[indexRn];
      sortedItems.removeAt(indexRn);
      sortedItems.insert(0, filterRN);
    }

    return sortedItems;
  }
}
