import 'package:soma_ui/cms/models/components/text_warning_cms_component.dart';

/// Representação do conteúdo para Order Details vindo do CMS.
class CmsOrderDetailsDocument {
  const CmsOrderDetailsDocument({
    required this.id,
    required this.attributes,
  });

  factory CmsOrderDetailsDocument.fromJson(Map<String, dynamic> json) {
    return CmsOrderDetailsDocument(
      id: json['id'],
      attributes:
          CmsOrderDetailsDocumentAttributes.fromJson(json['attributes']),
    );
  }

  final int id;
  final CmsOrderDetailsDocumentAttributes attributes;
}

class CmsOrderDetailsDocumentAttributes<T> {
  const CmsOrderDetailsDocumentAttributes({
    required this.createdAt,
    required this.updatedAt,
    required this.publishedAt,
    required this.textWarning,
  });

  factory CmsOrderDetailsDocumentAttributes.from<PERSON>son(
      Map<String, dynamic> json) {
    return CmsOrderDetailsDocumentAttributes(
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      publishedAt: DateTime.parse(json['publishedAt']),
      textWarning: json['warning_text'] is Map
          ? TextWarningCmsComponent.fromJson(json['warning_text'])
          : null,
    );
  }
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime publishedAt;
  final TextWarningCmsComponent? textWarning;
}
