import 'package:soma_ui/cms/models/models.dart';

class TextWarningCmsComponent extends CmsComponent {
  final String text;
  final bool isActive;

  TextWarningCmsComponent({
    required super.id,
    required super.componentType,
    required this.text,
    required this.isActive,
  });

  factory TextWarningCmsComponent.fromJson(Map<String, dynamic> json) {
    return TextWarningCmsComponent(
      id: json['id'],
      componentType: json['__component'] ?? '',
      text: json['text'],
      isActive: json['is_active'] ?? false,
    );
  }
}
