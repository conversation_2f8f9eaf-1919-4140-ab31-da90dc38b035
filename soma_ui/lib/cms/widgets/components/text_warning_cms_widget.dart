import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';

class TextWarningCmsWidget extends StatelessWidget {
  final String text;
  final bool isActive;

  const TextWarningCmsWidget(
      {super.key, required this.text, required this.isActive});

  factory TextWarningCmsWidget.fromComponent(
      TextWarningCmsComponent component) {
    return TextWarningCmsWidget(
      text: component.text,
      isActive: component.isActive,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!isActive) {
      return const SizedBox.shrink();
    }
    final tokens = context.designTokens;

    return Padding(
      padding: EdgeInsets.all(
        tokens.spacingStack.sm,
      ).copyWith(
        bottom: tokens.spacingStack.xxs,
      ),
      child: Container(
        padding: EdgeInsets.all(tokens.spacingStack.sm),
        color: tokens.colors.neutral.light1,
        child: Text(
          text,
          style: tokens.typography.typeStyles.body,
        ),
      ),
    );
  }
}
