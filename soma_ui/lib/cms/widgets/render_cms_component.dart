import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:soma_ui/cms/widgets/components/insider_recommended_showcase_cms_widget.dart';
import 'package:soma_ui/soma_ui.dart';

Widget renderCmsComponent(CmsComponent component) {
  return switch (component) {
    CarouselListCmsComponent() =>
      CarouselListCmsWidget.fromComponent(component),
    AccordionMenuCmsComponent() =>
      AccordionMenuCmsWidget.fromComponent(component),
    CategoryBannerCmsComponent() => CategoryBannerCmsWidget(component),
    CategoriesMosaicCmsComponent() =>
      CategoriesMosaicCmsWidget.fromCategoriesMosaicCmsComponent(component),
    SpotProductClockCmsComponent() => SpotProductClockCmsWidget(component),
    ShowCaseListCmsComponent() =>
      ShowCaseListCmsWidget.fromCmsComponent(component),
    CouponBannerCmsComponent() =>
      CouponBannerCmsWidget.fromCouponBannerCmsComponent(component),
    VideoGalleryCmsComponent() => VideoGalleryCmsWidget(component),
    SpotProductCmsComponent() =>
      SpotProductCmsWidget.fromSpotProductCmsComponent(component),
    ClockCmsComponent() => ClockCmsWidget(component),
    StoriesCmsComponent() => StoriesCmsWidget(component),
    MyInterestsBannerCmsComponent() =>
      MyInterestsBannerCmsWidget.fromComponent(component),
    ListLinksListCmsComponent() =>
      ListLinkCmsWidget.fromListLinksListCmsComponent(component),
    HighlightListCmsComponent() =>
      HightlightListCmsWidget.fromComponent(component),
    PersonalShowcaseCmsComponent() =>
      PersonalShowcaseCmsWidget.fromPersonalShowcaseCmsComponent(component),
    MyInterestsCardCmsComponent() =>
      MyInterestsCardCmsWidget.fromComponent(component),
    CarouselBannerCmsComponent() =>
      CarouselBannerCmsWidget.fromComponent(component),
    SignUpLoginCmsComponent() => SignUpLoginCmsWidget.fromComponent(component),
    ShowcaseBannerCmsComponent() =>
      ShowcaseBannerCmsWidget.fromShowcaseBannerCmsComponent(component),
    PackagingCmsComponent() => PackagingCmsWidget.fromComponent(component),
    CategoriesMosaicCompleteCmsComponent() =>
      CategoriesMosaicCompleteCmsWidget.fromComponent(component),
    InsiderRecommendedShowcaseCmsComponent() =>
      InsiderRecommendedShowcaseCmsWidget.fromComponent(component),
    ClusterComponentCategoryBannerCmsComponent() =>
      CategoryBannerCmsWidget(component.categoryBanner),
    ClusterComponentCouponBannerCmsComponent() =>
      CouponBannerCmsWidget.fromCouponBannerCmsComponent(
          component.couponBanner),
    FullLookCarouselCmsComponent() =>
      FullLookCarouselCmsWidget.fromComponent(component),
    TextWarningCmsComponent() => TextWarningCmsWidget.fromComponent(component),
    // Cadastre o seu componente acima
    ParseErrorCmsComponent() when kDebugMode => _ParseErrorWarning(component),
    _ when kDebugMode => _UnknownComponentWarning(component),
    _ => const SizedBox.shrink(),
  };
}

class _UnknownComponentWarning extends StatelessWidget {
  const _UnknownComponentWarning(this.component);
  final CmsComponent component;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.red,
      alignment: Alignment.center,
      child: Text(
        'Não foi possível renderizar o componente ${component.componentType} (${component.runtimeType})',
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _ParseErrorWarning extends StatelessWidget {
  const _ParseErrorWarning(this.component);
  final ParseErrorCmsComponent component;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.red,
      alignment: Alignment.center,
      child: Text(
        'Error de parse: \n${component.details} \n ${component.error} \n ${component.stackTrace}',
      ),
    );
  }
}
