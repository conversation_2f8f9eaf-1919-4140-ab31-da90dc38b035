import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:soma_app_notification_center_module/src/models/notification_center/models.dart';
import 'package:soma_core/modules/checkout/models/index.dart';
import 'package:soma_ui/soma_ui.dart';

import '../models/models.dart';
import 'notification_center_item.dart';

class NotificationCenterHomeMessage extends StatefulWidget {
  const NotificationCenterHomeMessage({
    Key? key,
    required this.message,
    this.onHide,
  }) : super(key: key);

  final NotificationCenterMessage message;
  final VoidCallback? onHide;

  @override
  State<NotificationCenterHomeMessage> createState() =>
      _NotificationCenterHomeMessageState();
}

class _NotificationCenterHomeMessageState
    extends State<NotificationCenterHomeMessage>
    with AnalyticsEventDispatcherStateMixin, AppRoutesStateMixin {
  late final redirectUtils = context.locateService<RedirectUtils>();

  @override
  void initState() {
    super.initState();
    scheduleMicrotask(() {
      dispatchViewPromotionEvent(
        screenClass: 'NotificationCenterHomeMessage',
        context,
        promotionName: widget.message.title,
        creativeName: widget.message.message,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final icons = tokens.icons;
    final colors = tokens.colors;
    final notificationForegroundColor = colors.typography.pure1;
    final notificationListItemTheme =
        Theme.of(context).extension<NotificationListItemTheme>();
    final leadingImageSize = notificationListItemTheme?.leadingImageSize;
    final messageType = widget.message.messageType;

    final notificationHomeMessageTheme =
        Theme.of(context).extension<NotificationCenterHomeMessageTheme>();

    return SMHomeNotification(
      title: Text(widget.message.title),
      message: Text(widget.message.message),
      backgroundColor: widget.message.alertBackgroundColor?.toColor(),
      alpha: widget.message.alpha,
      textColor: widget.message.alertTextColor?.toColor(),
      leading: widget.message.iconProvider == null
          ? null
          : Image(
              image: widget.message.iconProvider!,
              color: notificationForegroundColor,
              width: leadingImageSize ?? 24,
              height: leadingImageSize ?? 24,
            ),
      trailing: Icon(
        messageType == NotificationMessageType.expandedMessageBanner
            ? icons.add
            : icons.arrowRight,
        color: widget.message.alertTextColor?.toColor() ??
            notificationForegroundColor,
      ),
      duration:
          notificationHomeMessageTheme?.duration ?? const Duration(seconds: 5),
      onTap: () {
        _getActionOnTap();
        analyticsController.logHomeNotificationCenterMessageAccess(
          classification: widget.message.clientClassification.toString(),
        );
        dispatchSelectPromotionEvent(
          screenClass: 'NotificationCenterHomeMessage',
          promotionName: widget.message.title,
          creativeName: widget.message.message,
        );
      },
      onHide: widget.onHide,
      titleStyle: notificationHomeMessageTheme?.titleStyle?.copyWith(
        color: widget.message.alertTextColor?.toColor(),
      ),
      messageStyle: notificationHomeMessageTheme?.messageStyle?.copyWith(
        color: widget.message.alertTextColor?.toColor(),
      ),
    );
  }

  _getActionOnTap() {
    if (widget.message.messageType ==
        NotificationMessageType.expandedMessageBanner) {
      return customShowBottomSheet(_ExpandedNotificationBottomSheet(
        callToAction: widget.message.callToAction,
        navigateTo: widget.message.navigateTo,
        title: widget.message.title,
        message: widget.message.message,
        textContent: widget.message.textContent,
        bannerImage: widget.message.bannerProvider,
        coupon: widget.message.coupon,
      ));
    }

    return widget.message.redirectType ==
            RedirectType.navigateToNotificationCenter
        ? navigateToNamed('/notification-center')
        : context.dynamicNavigate(widget.message.navigateTo);
  }
}

class _ExpandedNotificationBottomSheet extends StatefulWidget {
  final String navigateTo;
  final String title;
  final String? callToAction;
  final String? message;
  final List<CmsTextContent>? textContent;
  final ImageProvider<Object>? bannerImage;
  final String? coupon;

  const _ExpandedNotificationBottomSheet({
    required this.navigateTo,
    required this.title,
    this.callToAction,
    this.message,
    this.textContent,
    this.bannerImage,
    this.coupon,
  });

  @override
  State<_ExpandedNotificationBottomSheet> createState() =>
      _ExpandedNotificationBottomSheetState();
}

class _ExpandedNotificationBottomSheetState
    extends State<_ExpandedNotificationBottomSheet> with CouponPromotionMixin {
  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final orderForm = context.locateService<Rxn<OrderForm>>();

    final isCouponApplied = orderForm.value?.isCouponApplied(coupon) ?? false;

    return SMBottomSheet(
      title: widget.title,
      subtitle: widget.message,
      buttonTitle: widget.callToAction,
      hasMinWidth: true,
      onTapSecondButton: () async {
        await _handleOnTapSecondButton(
            isCouponApplied: isCouponApplied, tokens: tokens);
      },
      secondaryButtonStyle: isCouponApplied
          ? SMButtonStyle(
              border: DashedBorder(color: tokens.colors.neutral.medium1),
            )
          : null,
      secondButtonTrailing: isCouponApplied
          ? Icon(
              tokens.icons.copy,
              size: 16.0,
            )
          : null,
      secondButtonIsColumn: true,
      secondButtonTitle: _getSecondButtonTitle(isCouponApplied),
      childHeightFactor: double.infinity,
      onTap: () {
        context.dynamicNavigate(widget.navigateTo);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextContent(tokens),
          if (widget.bannerImage != null)
            SMImage(
              image: widget.bannerImage!,
            )
        ],
      ),
    );
  }

  Future<void> _handleOnTapSecondButton(
      {required bool isCouponApplied, required DesignTokens tokens}) async {
    if (widget.coupon.isNullOrEmpty) {
      return;
    }

    if (isCouponApplied) {
      await Clipboard.setData(ClipboardData(text: widget.coupon!));
      await showSnackBar(
        SMSnackBarGetX(
          textLabel: 'Cupom copiado',
          designTokens: tokens,
        ),
      );
      return;
    }

    await onTapCoupon();
  }

  String? _getSecondButtonTitle(bool isCouponApplied) {
    if (widget.coupon.isNullOrEmpty) {
      return null;
    }
    if (isCouponApplied) {
      return widget.coupon;
    }

    return 'Aplicar cupom';
  }

  Widget _buildTextContent(DesignTokens tokens) {
    final textContent = widget.textContent;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      textDirection: TextDirection.ltr,
      children: [
        ...?textContent
            ?.map(
              (content) => _TextContent(
                textContent: content,
              ),
            )
            .toList(),
      ],
    );
  }

  @override
  String? get coupon => widget.coupon;

  @override
  String get snackbarTextForCouponApplied =>
      'Cupom aplicado na mochila com sucesso!';

  @override
  String get snackbarTextForEmptyBag =>
      'Ops! você precisa ter 1 ou mais produtos na sua mochila';
}

class _TextContent extends StatelessWidget {
  const _TextContent({
    Key? key,
    required this.textContent,
  }) : super(key: key);

  final CmsTextContent textContent;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    if (textContent.title == null && textContent.description == null) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (textContent.title.isNotNullOrEmpty) ...[
          Text(
            textAlign: TextAlign.start,
            textContent.title ?? "",
            style: tokens.typography.typeStyles.subtitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(
            height: tokens.spacingStack.xs,
          ),
        ],
        if (textContent.description.isNotNullOrEmpty) ...[
          Text(
            textContent.description?.replaceAll("\\n", "\n") ?? '',
            style: tokens.typography.typeStyles.subtitle,
          ),
          SizedBox(
            height: tokens.spacingStack.md,
          )
        ],
      ],
    );
  }
}

class NotificationCenterHomeMessageTheme
    extends ThemeExtension<NotificationCenterHomeMessageTheme> {
  final TextStyle? titleStyle;
  final TextStyle? messageStyle;
  final Duration? duration;

  const NotificationCenterHomeMessageTheme({
    this.titleStyle,
    this.messageStyle,
    this.duration,
  });

  @override
  ThemeExtension<NotificationCenterHomeMessageTheme> copyWith(
      {TextStyle? titleStyle, TextStyle? messageStyle, Duration? duration}) {
    return NotificationCenterHomeMessageTheme(
      titleStyle: titleStyle ?? this.titleStyle,
      messageStyle: messageStyle ?? this.messageStyle,
      duration: duration ?? this.duration,
    );
  }

  @override
  ThemeExtension<NotificationCenterHomeMessageTheme> lerp(
    covariant NotificationCenterHomeMessageTheme? other,
    double t,
  ) {
    return NotificationCenterHomeMessageTheme(
      titleStyle: t < 0.5 ? titleStyle : other?.titleStyle,
      messageStyle: t < 0.5 ? messageStyle : other?.messageStyle,
      duration: t < 0.5 ? duration : other?.duration,
    );
  }
}
