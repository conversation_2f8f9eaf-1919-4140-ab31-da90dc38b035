import 'package:flutter/material.dart' hide ButtonTheme;
import 'package:get/get.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';

import 'icon_tokens.dart';
import 'product_details.dart';
import 'sizes_table.dart';
import 'text_transform.dart';
import 'terms_and_messages_tokens.dart';
import 'widgets/clock_layout.dart';

DesignTokens designTokensBuilder() {
  final offDesignTokens = DesignTokens(
    colors: _colors,
    typography: _typography,
    borderRadius: const DTBorderRadius.squared(
      radiusLabel: 0.0,
      radiusSmall: 4.0,
    ),
    spacingStack: _spacingStack,
    icons: _icons,
    termsAndMessages: offTermsAndMessagesTokens,
    sizes: const DTSizes(defaultConfirmationHeaderButtonSize: 221.0),
    textTransform: OffTextTransform(),
    pdpTheme: _pdpTheme,
    genericErrorTheme: GenericErrorTheme(
      titleStyle: _typography.typeStyles.headlineMd.copyWith(fontSize: 24),
      subtitleStyle: _typography.typeStyles.headlineSm.copyWith(
        color: _colors.typography.light2,
      ),
    ),
    sizesTableTheme: const SizesTableTheme(
      sizesTable: OffSizesTable(),
      iSasyncSizesTable: true,
      tableHeaderHeight: 48.0,
      tableRowHeight: 48.0,
      sizesTableBottomSheetHeaderTitle: 'GUIA DE TAMANHOS X',
      sizesTableBottomSheetHeaderSubtitle: 'MEDIDAS DO CORPO X',
      hasExchangeContainer: true,
      hasNeedHelpContainer: true,
      hideCloseButton: true,
      hideButton: true,
      showDragHandling: true,
      measuresImages: {
        'feminino': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/bodymeasures_female.png'),
        'masculino': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/bodymeasures_male.png'),
        'infantil': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/bodymeasures_kids.png'),
      },
      tableImageTitle: 'Como me medir?',
      tableImageSubTitle:
          'Tire as medidas do seu corpo de acordo com as instruções abaixo.',
      roundedNumerationStyle: false,
      brandImages: {
        'animale': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/animale_brand_table.png'),
        'animale_jeans': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/animale_jeans_brand_table.png'),
        'carol_bassi': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/carol_bassi_brand_table.png'),
        'fabula': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/fabula_brand_table.png'),
        'farm': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/farm_brand_table.png'),
        'maria_filo': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/mf_brand_table.png'),
        'nv': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/nv_brand_table.png'),
        'reserva': NetworkImage(
            'https://lojaoffpremium.vteximg.com.br/arquivos/reserva_brand_t.png'),
      },
    ),
    bottomSheetSizeTableTheme: BottomSheetSizeTableTheme(
      hasScrollableHeader: true,
      padding: const EdgeInsets.all(0),
      expandedButton: true,
      headerTitlePadding: const EdgeInsets.only(
        bottom: 32,
        left: 24,
      ),
      headerPadding: const EdgeInsets.only(
        bottom: 16,
        top: 24,
        left: 24,
        right: 24,
      ),
      bottomSheetHeightFactor: 1,
      buttonStyle: SMButtonStyle(
        padding: const EdgeInsets.symmetric(
          vertical: 10,
          horizontal: 10,
        ),
        backgroundColor: Colors.transparent,
        borderColor: _colors.neutral.medium1,
      ),
      textStyle: _typography.typeStyles.body.copyWith(
        color: _colors.typography.dark1,
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
      arrowIconSize: 22,
    ),
    orderListTheme: OrderListTheme(returnWidgetColor: _colors.neutral.pure1),
    catalogFiltersTheme: CatalogFiltersTheme(icons: _icons),
    emptyBagTheme: EmptyBagTheme(
      imageSize: 60,
      buttonTextColor: _colors.brand.pure1,
      buttonBorderColor: _colors.brand.pure1,
    ),
    spotProductTheme: _spotProductTheme,
    bagTheme: BagTheme(
      productTagsTheme: ProductTagsTheme(
        tagsAlignment: MainAxisAlignment.start,
        brandTag: ProductTagStyle(
          backgroundColor: _colors.brand.pure1,
          textColor: _colors.typography.pure1,
        ),
        productTagPlacement: ProductTagPlacement.start,
        prismicTags: false,
      ),
      productCardTheme: const BagProductCardTheme(
        hasDescriptionTextSizeButton: true,
      ),
    ),
    wishlistTheme: WishlistTheme(
      productTagsTheme: ProductTagsTheme(
        tagsAlignment: MainAxisAlignment.start,
        brandTag: ProductTagStyle(
          backgroundColor: _colors.brand.pure1,
          textColor: _colors.typography.pure1,
        ),
        productTagPlacement: ProductTagPlacement.start,
        prismicTags: false,
      ),
    ),
    spotProductClockTheme: SpotProductClockTheme(
      simpleClockBuilder: OffPremiumClockLayout.new,
    ),
    emptySearchTheme: _emptySearchTheme,
    clockTheme: const ClockTheme(layoutBuilder: OffPremiumClockLayout.new),
    orderStatusTheme: _orderStatusTheme,
    cmsAccordionMenuTheme: CmsAccordionMenuTheme(
      itemTitleStyle: _typography.typeStyles.body,
    ),
    orderStatusTimelineTheme: OrderStatusTimelineTheme(
      titleTextStyle: _typography.typeStyles.body.copyWith(
        fontWeight: FontWeight.normal,
        color: _colors.typography.pure2,
      ),
      dateTextStyle: _typography.typeStyles.bodySm.copyWith(
        color: _colors.typography.light2,
      ),
      statusHeight: 60.0,
      dashQuantity: 5,
      showCanceled: true,
      outForDeliveryLabel: "Saiu para entrega",
      deliveryDateFormatter: DateUtilsSM.dateAndHour,
    ),
    productCardTheme: _productCardTheme,
    orderConfirmationTheme: const OrderConfirmationTheme(
      detailsTheme: ConfirmedOrderDetailsTheme(
        showDeliverySLAAfterOrderDetails: true,
        showOrderIDInHeader: true,
      ),
    ),
    orderDetailsTheme: const OrderDetailsTheme(
      showOrderTotalPriceInTrailing: true,
    ),
    signUpTheme:
        const SignUpTheme(showPrivacyNotice: ShowPrivacyNotice.allPages),
    buttonTheme: const ButtonTheme(
      showSquaredFormatBackgroundLoadingAnimation: false,
    ),
    loginBottomSheetTheme: const LoginBottomSheetTheme(
      headerPadding: EdgeInsets.zero,
      titlePadding: EdgeInsets.zero,
    ),
    passwordInputTheme: const PasswordInputTheme(
      alwaysShowSecurityChecks: true,
    ),
    defaultButtonTheme: DefaultButtonTheme(
      disabledForegroundColor: _colors.neutral.light2,
    ),
    paymentMethodTheme: const PaymentMethodTheme(
      pixInstallmentsHintBackgroundColor: Color(0xFFE5E5EA),
      pixInstallmentsTextSize: 14,
    ),
    checkoutResumeTheme: CheckoutResumeTheme(
      backgrondColorMessageErrorWithPaymentCreditCard:
          _colors.feedback.pureError,
      colorMessageErrorWithPaymentCreditCard: const Color(0xFFFAFAFA),
      colorCreditCardWithErrorWithPayment: _colors.feedback.pureError,
    ),
    contentDevolutionTheme: ContentDevolutionTheme(
      storeUrl: 'https://www.offpremium.com.br/nossas-lojas',
      cardColor: _colors.neutral.light1,
      borderRadius: BorderRadius.circular(0),
      titleStyle: _typography.typeStyles.bodyMd?.copyWith(
        color: _colors.typography.pure2,
      ),
      subtitleStyle: _typography.typeStyles.bodyXs?.copyWith(
        color: _colors.typography.pure2,
      ),
      bottomSheetTitleStyle: _typography.typeStyles.bodyXl?.copyWith(
        color: _colors.typography.pure2,
      ),
      buttonStyle: _typography.typeStyles.bodySm.copyWith(
        color: _colors.typography.pure2,
      ),
    ),
  );

  return offDesignTokens;
}

const _colors = DTColors(
  brand: BrandColors(
    pure1: Color(0xFF000000),
    pure2: Color(0xFFEAEEDC),
    pure3: Color(0xFFF2FBFF),
    dark1: Color(0xFFA1061A),
  ),
  typography: DTColorTypography(
    pure1: Color(0xFFFFFFFF),
    light2: Color(0xFF737378),
    pure2: Color(0xFF000000),
  ),
  neutral: NeutralColor(
    pure1: Color(0xFFFFFFFF),
    light1: Color(0xFFF2F2F7),
    medium1: Color(0xFFE5E5EA),
    light2: Color(0xFF737378),
    pure2: Color(0xFF000000),
  ),
  feedback: FeedBackColors(
    pureError: Color(0xFFD10923),
    lightError: Color(0xFFFFF3F2),
    mediumError: Color(0XFFA03127),
    pureSuccess: Color(0XFF10B85D),
    lightSuccess: Color(0XFFC8EFDA),
    mediumSuccess: Color(0XFF0B8E48),
    darkSuccess: Color(0XFF06532A),
  ),
  colorsWithOpacity: ColorsWithOpacity(
    colorNeutralLightPureOpacityLight: .16,
    colorNeutralLightPureOpacityIntense: .64,
  ),
);

const _fontFamilyPrimary = "Lato";
const _fontSizes = FontSizes(
  ul: 48,
  xxxl: 48,
  xxl: 48,
  sm: 18,
  xxs: 18,
  us: 16,
  xxus: 14,
  xxxus: 10,
);

final _typography = DTTypography(
  colors: _colors.typography,
  fontFamilyPrimary: _fontFamilyPrimary,
  fontSizes: _fontSizes,
  defaultFontWeight: FontWeight.normal,
  headlineLg: TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: _fontSizes.sm,
  ),
  headlineMd: TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: _fontSizes.xxs,
  ),
  headlineSm: TextStyle(
    fontSize: _fontSizes.xxs,
  ),
  subtitle: TextStyle(
    fontSize: _fontSizes.xxus,
  ),
);

const _spacingStack = DTSpacingStack(lg: 16, ul: 40);
const _spacingInline = DTSpacingInline();

const _icons = offIconTokens;

final _orderStatusTheme = OrderStatusTheme.raw(
  tagPadding: EdgeInsets.symmetric(
    vertical: _spacingStack.xxs,
    horizontal: 12,
  ),
  statusLabelList: {
    OrderStatus.created: OrderStatusLabel(
      label: 'Pagamento criado',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.paymentPending: OrderStatusLabel(
      label: 'Aprovando pagamento',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.approvedPayment: OrderStatusLabel(
      label: 'Pagamento aprovado',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.onCarriage: OrderStatusLabel(
      label: 'Saiu para entrega',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.cancellationRequested: OrderStatusLabel(
      label: 'Cancelamento solicitado',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.invoiced: OrderStatusLabel(
      label: 'Pedido confirmado',
      color: _colors.neutral.medium1,
      textColor: _colors.typography.pure2,
    ),
    OrderStatus.delivered: OrderStatusLabel(
      label: 'Pedido entregue',
      color: _colors.feedback.darkSuccess,
      textColor: _colors.typography.pure1,
    ),
    OrderStatus.canceled: OrderStatusLabel(
      label: 'Pedido cancelado',
      color: _colors.feedback.mediumError,
      textColor: _colors.typography.pure1,
    ),
  },
);

final _pdpTheme = PdpTheme(
  mainContent: PdpMainContent.productImageAndInformation,
  productInformationTheme: const ProductInformationTheme(
    layout: ProductInformationLayout.rowBased,
  ),
  favoriteButtonPlacement: FavoriteButtonPlacement.headerRight,
  shareButton: const ShareButton(
    enabled: true,
    borderColor: Colors.transparent,
    borderRadius: 0.0,
  ),
  showBottomBarAtHeight: (Get.height / 2),
  showProductBrandSender: true,
  showBuyButtonOnProductInformation: true,
  productDetails: const OffProductDetails(),
  belowSelectSizeActions: [
    BelowSelectSizeActions.measurements,
  ],
  productTagsTheme: ProductTagsTheme(
    prismicTags: true,
    tagsAlignment: MainAxisAlignment.spaceBetween,
    brandTag: ProductTagStyle(
      backgroundColor: _colors.brand.pure1,
      textColor: _colors.typography.pure1,
    ),
    discountTag: ProductTagStyle(
      backgroundColor: _colors.neutral.light1,
      textColor: _colors.typography.pure2,
    ),
  ),
  bottomBarBuyButtonWidth: 114.0,
  bottomBarBuyTextContainerWidth: 170.0,
);

final _spotProductTheme = SpotProductTheme(
  resizeVtexImage: true,
  productTagsTheme: ProductTagsTheme(
    prismicTags: true,
    maxTextLength: 11,
    tagsAlignment: MainAxisAlignment.start,
    brandTag: ProductTagStyle(
      backgroundColor: _colors.brand.pure1,
      textColor: _colors.typography.pure1,
    ),
    discountTag: ProductTagStyle(
      backgroundColor: _colors.neutral.light1,
      textColor: _colors.typography.pure2,
    ),
    productTagPlacement: ProductTagPlacement.both,
  ),
  getInfoLeftPadding: (density) {
    return density > 2 ? _spacingInline.xs : _spacingInline.sm;
  },
);

final _emptySearchTheme = EmptySearchTheme(
  otherTermButtonStyle: OtherTermButtonStyle(
    maxWidth: double.infinity,
    textColor: _colors.typography.pure1,
    textStyle: _typography.typeStyles.headlineSm.copyWith(
      fontSize: 16,
    ),
    backgroundColor: _colors.neutral.pure2,
    isOutline: false,
  ),
  hasSearchIcon: false,
  enablePrismicUi: true,
  subtitleStyle: _typography.typeStyles.body.copyWith(fontSize: 14),
  marginTop: _spacingStack.md,
  marginTopButton: _spacingStack.xxxl,
  contentAlignment: MainAxisAlignment.spaceEvenly,
  buttonHeight: ButtonHeight.large,
);

const _productCardTheme = ProductCardTheme(
  resizeVtexImage: true,
);
